"""
Auto Hold System - Ultra-High Performance Seat Holding
Single optimized system for sub-100ms seat holding with detailed performance benchmarking.
Supports up to 1000 concurrent seats with pre-cached tokens and minimal overhead.
"""

import asyncio
import json
import logging
import time
import threading
import secrets
from typing import Optional, Dict, List, Any, Callable
from collections import deque, defaultdict
from concurrent.futures import ThreadPoolExecutor
import httpx

# Import existing helper functions
from helper import build_channel_keys, async_make_request
from chart_token_manager import generate_x_signature
from token_retrieval import get_hold_token, get_cached_event_id

logger = logging.getLogger("webook_pro")

# Constants for optimization
SEATSIO_IP = "*************"
HOLD_URL = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
MAX_CONCURRENT_HOLDS = 1000
TOKEN_POOL_SIZE = 50
TOKEN_REFRESH_THRESHOLD = 300  # 5 minutes

class PerformanceTracker:
    """Detailed performance tracking for auto-hold operations"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.lock = threading.RLock()
        
        # Timing metrics (all in milliseconds)
        self.websocket_to_decision = deque(maxlen=max_samples)
        self.decision_to_request = deque(maxlen=max_samples)
        self.request_to_response = deque(maxlen=max_samples)
        self.total_response_times = deque(maxlen=max_samples)
        
        # Success/failure tracking
        self.attempts = 0
        self.successes = 0
        self.failures = 0
        self.token_failures = 0
        self.network_failures = 0
        
        # Current operation tracking
        self.active_operations = {}
        
    def start_operation(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> str:
        """Start tracking a new hold operation"""
        operation_id = f"{seat_id}_{int(time.time() * 1000000)}"
        start_time = time.perf_counter()
        
        with self.lock:
            self.attempts += 1
            self.active_operations[operation_id] = {
                'seat_id': seat_id,
                'start_time': start_time,
                'websocket_timestamp': websocket_timestamp or start_time,
                'decision_time': None,
                'request_time': None,
                'response_time': None
            }
            
        return operation_id
    
    def record_decision(self, operation_id: str):
        """Record when decision to hold was made"""
        decision_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['decision_time'] = decision_time
                
                # Calculate websocket to decision time
                ws_to_decision_ms = (decision_time - op['websocket_timestamp']) * 1000
                self.websocket_to_decision.append(ws_to_decision_ms)
    
    def record_request_start(self, operation_id: str):
        """Record when network request started"""
        request_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['request_time'] = request_time
                
                # Calculate decision to request time
                if op['decision_time']:
                    decision_to_req_ms = (request_time - op['decision_time']) * 1000
                    self.decision_to_request.append(decision_to_req_ms)
    
    def complete_operation(self, operation_id: str, success: bool, failure_reason: str = None):
        """Complete an operation and record final metrics"""
        response_time = time.perf_counter()
        
        with self.lock:
            if operation_id not in self.active_operations:
                return
                
            op = self.active_operations[operation_id]
            op['response_time'] = response_time
            
            # Calculate final timings
            if op['request_time']:
                req_to_resp_ms = (response_time - op['request_time']) * 1000
                self.request_to_response.append(req_to_resp_ms)
            
            total_time_ms = (response_time - op['start_time']) * 1000
            self.total_response_times.append(total_time_ms)
            
            # Update counters
            if success:
                self.successes += 1
            else:
                self.failures += 1
                if failure_reason == 'token':
                    self.token_failures += 1
                elif failure_reason == 'network':
                    self.network_failures += 1
            
            # Log detailed performance info
            seat_id = op['seat_id']
            ws_to_decision = (op['decision_time'] - op['websocket_timestamp']) * 1000 if op['decision_time'] else 0
            decision_to_req = (op['request_time'] - op['decision_time']) * 1000 if op['request_time'] and op['decision_time'] else 0
            req_to_resp = req_to_resp_ms if op['request_time'] else 0
            
            status = "✅ SUCCESS" if success else f"❌ FAILED ({failure_reason})"
            logger.info(f"🎯 HOLD PERFORMANCE [{seat_id}] {status}")
            logger.info(f"   📊 WS→Decision: {ws_to_decision:.2f}ms | Decision→Request: {decision_to_req:.2f}ms | Request→Response: {req_to_resp:.2f}ms | TOTAL: {total_time_ms:.2f}ms")
            
            # Clean up
            del self.active_operations[operation_id]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        with self.lock:
            def avg(deque_obj):
                return sum(deque_obj) / len(deque_obj) if deque_obj else 0
            
            return {
                'attempts': self.attempts,
                'successes': self.successes,
                'failures': self.failures,
                'success_rate': (self.successes / self.attempts * 100) if self.attempts > 0 else 0,
                'avg_total_time_ms': avg(self.total_response_times),
                'avg_ws_to_decision_ms': avg(self.websocket_to_decision),
                'avg_decision_to_request_ms': avg(self.decision_to_request),
                'avg_request_to_response_ms': avg(self.request_to_response),
                'token_failures': self.token_failures,
                'network_failures': self.network_failures,
                'active_operations': len(self.active_operations)
            }

class TokenPool:
    """High-performance token pool with pre-cached tokens"""
    
    def __init__(self, pool_size: int = TOKEN_POOL_SIZE):
        self.pool_size = pool_size
        self.tokens = deque()
        self.token_expiry = {}
        self.lock = threading.RLock()
        self.refresh_in_progress = False
        
        # Start background token refresh
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()
    
    def get_token(self) -> Optional[str]:
        """Get a valid token from the pool"""
        with self.lock:
            # Remove expired tokens
            current_time = time.time()
            while self.tokens:
                token = self.tokens[0]
                if token in self.token_expiry and self.token_expiry[token] > current_time + TOKEN_REFRESH_THRESHOLD:
                    return self.tokens.popleft()
                else:
                    # Token expired or about to expire
                    expired_token = self.tokens.popleft()
                    self.token_expiry.pop(expired_token, None)
            
            return None
    
    def _refresh_loop(self):
        """Background thread to keep token pool filled"""
        while True:
            try:
                with self.lock:
                    needed_tokens = self.pool_size - len(self.tokens)
                    if needed_tokens <= 0 or self.refresh_in_progress:
                        time.sleep(1)
                        continue
                    
                    self.refresh_in_progress = True
                
                # Get event ID
                event_id = get_cached_event_id()
                if not event_id:
                    time.sleep(5)
                    continue
                
                # Create tokens asynchronously
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    tasks = [get_hold_token(event_id) for _ in range(min(needed_tokens, 10))]
                    new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
                    
                    # Add valid tokens to pool
                    current_time = time.time()
                    with self.lock:
                        for token in new_tokens:
                            if isinstance(token, str) and token:
                                self.tokens.append(token)
                                # Assume 15 minute expiry
                                self.token_expiry[token] = current_time + (15 * 60)
                                
                        logger.info(f"🔄 Token pool refreshed: {len(self.tokens)}/{self.pool_size} tokens available")
                        
                finally:
                    loop.close()
                    with self.lock:
                        self.refresh_in_progress = False
                        
            except Exception as e:
                logger.error(f"Error refreshing token pool: {e}")
                with self.lock:
                    self.refresh_in_progress = False
                    
            time.sleep(10)  # Check every 10 seconds

class AutoHoldSystem:
    """Ultra-high performance auto-hold system"""
    
    def __init__(self, event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None):
        self.event_key = event_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        
        # Performance tracking
        self.performance = PerformanceTracker()
        
        # Token management
        self.token_pool = TokenPool()
        
        # HTTP client pool for maximum performance
        self.http_clients = []
        self.client_lock = threading.Lock()
        self._init_http_clients()
        
        # Thread pool for concurrent operations
        self.executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_HOLDS, thread_name_prefix="AutoHold")
        
        # Pre-built request template
        self.request_template = {
            'events': [self.event_key],
            'holdToken': None,
            'objects': [{'objectId': None}],
            'channelKeys': build_channel_keys(self.channel_keys, self.team_id),
            'validateEventsLinkedToSameChart': True,
        }
        
        # Pre-built headers template
        self.headers_template = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'x-client-tool': 'Renderer',
            'x-browser-id': None,
            'x-signature': None
        }
        
        # Callbacks
        self.success_callback: Optional[Callable[[str, str], None]] = None
        self.failure_callback: Optional[Callable[[str, str], None]] = None
        
        logger.info(f"🚀 AutoHoldSystem initialized for event {event_key}")
    
    def _init_http_clients(self):
        """Initialize pool of HTTP clients for maximum performance"""
        for _ in range(min(MAX_CONCURRENT_HOLDS, 100)):
            client = httpx.Client(
                timeout=httpx.Timeout(3.0),
                verify=False,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
            self.http_clients.append(client)
    
    def _get_http_client(self) -> httpx.Client:
        """Get an HTTP client from the pool"""
        with self.client_lock:
            if self.http_clients:
                return self.http_clients.pop()
            else:
                # Create new client if pool is empty
                return httpx.Client(
                    timeout=httpx.Timeout(3.0),
                    verify=False,
                    limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
                )
    
    def _return_http_client(self, client: httpx.Client):
        """Return HTTP client to pool"""
        with self.client_lock:
            if len(self.http_clients) < 100:
                self.http_clients.append(client)
            else:
                client.close()

    def set_callbacks(self, success_callback: Callable[[str, str], None], failure_callback: Callable[[str, str], None]):
        """Set callbacks for success and failure events"""
        self.success_callback = success_callback
        self.failure_callback = failure_callback

    def hold_seat(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
        """
        Ultra-fast seat holding - main entry point
        Returns immediately, actual hold happens asynchronously
        """
        # Start performance tracking
        operation_id = self.performance.start_operation(seat_id, websocket_timestamp)

        # Record decision time (immediate)
        self.performance.record_decision(operation_id)

        # Submit to thread pool for immediate return
        self.executor.submit(self._hold_seat_sync, seat_id, operation_id)

        return True

    def _hold_seat_sync(self, seat_id: str, operation_id: str):
        """Synchronous seat holding with maximum performance"""
        client = None
        token = None

        try:
            # Get token from pool
            token = self.token_pool.get_token()
            if not token:
                self.performance.complete_operation(operation_id, False, 'token')
                if self.failure_callback:
                    self.failure_callback(seat_id, "No tokens available")
                return False

            # Get HTTP client
            client = self._get_http_client()

            # Build request
            request_data = self.request_template.copy()
            request_data['holdToken'] = token
            request_data['objects'] = [{'objectId': seat_id}]

            # Build headers
            headers = self.headers_template.copy()
            headers['x-browser-id'] = secrets.token_hex(8)

            # Serialize and sign
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['x-signature'] = generate_x_signature(body_str)

            # Record request start
            self.performance.record_request_start(operation_id)

            # Make request
            response = client.post(
                HOLD_URL,
                content=body_str,
                headers=headers,
                timeout=3.0
            )

            # Process response
            if response.status_code == 204:
                self.performance.complete_operation(operation_id, True)
                if self.success_callback:
                    self.success_callback(seat_id, token)
                return True
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:100]}"
                self.performance.complete_operation(operation_id, False, 'network')
                if self.failure_callback:
                    self.failure_callback(seat_id, error_msg)
                return False

        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            self.performance.complete_operation(operation_id, False, 'network')
            if self.failure_callback:
                self.failure_callback(seat_id, error_msg)
            return False

        finally:
            if client:
                self._return_http_client(client)

    def hold_multiple_seats(self, seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
        """
        Hold multiple seats concurrently for maximum speed
        """
        if len(seat_ids) > MAX_CONCURRENT_HOLDS:
            logger.warning(f"Requested {len(seat_ids)} seats, limiting to {MAX_CONCURRENT_HOLDS}")
            seat_ids = seat_ids[:MAX_CONCURRENT_HOLDS]

        # Submit all holds concurrently
        for seat_id in seat_ids:
            self.hold_seat(seat_id, websocket_timestamp)

        logger.info(f"🎯 Submitted {len(seat_ids)} concurrent hold operations")
        return True

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics"""
        return self.performance.get_stats()

    def log_performance_summary(self):
        """Log a detailed performance summary"""
        stats = self.get_performance_stats()

        logger.info("📊 AUTO-HOLD PERFORMANCE SUMMARY")
        logger.info(f"   🎯 Total Attempts: {stats['attempts']}")
        logger.info(f"   ✅ Successes: {stats['successes']} ({stats['success_rate']:.1f}%)")
        logger.info(f"   ❌ Failures: {stats['failures']} (Token: {stats['token_failures']}, Network: {stats['network_failures']})")
        logger.info(f"   ⏱️  Average Total Time: {stats['avg_total_time_ms']:.2f}ms")
        logger.info(f"   📡 WebSocket→Decision: {stats['avg_ws_to_decision_ms']:.2f}ms")
        logger.info(f"   🔄 Decision→Request: {stats['avg_decision_to_request_ms']:.2f}ms")
        logger.info(f"   🌐 Request→Response: {stats['avg_request_to_response_ms']:.2f}ms")
        logger.info(f"   🔄 Active Operations: {stats['active_operations']}")

    def cleanup(self):
        """Clean up resources"""
        self.executor.shutdown(wait=True)

        with self.client_lock:
            for client in self.http_clients:
                client.close()
            self.http_clients.clear()

        logger.info("🧹 AutoHoldSystem cleaned up")

# Global instance for easy access
_auto_hold_system: Optional[AutoHoldSystem] = None

def initialize_auto_hold(event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None) -> AutoHoldSystem:
    """Initialize the global auto-hold system"""
    global _auto_hold_system

    if _auto_hold_system:
        _auto_hold_system.cleanup()

    _auto_hold_system = AutoHoldSystem(event_key, channel_keys, team_id, proxy)
    return _auto_hold_system

def get_auto_hold_system() -> Optional[AutoHoldSystem]:
    """Get the global auto-hold system instance"""
    return _auto_hold_system

def hold_seat_fast(seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
    """Fast seat holding using global system"""
    if _auto_hold_system:
        return _auto_hold_system.hold_seat(seat_id, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def hold_multiple_seats_fast(seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
    """Fast multiple seat holding using global system"""
    if _auto_hold_system:
        return _auto_hold_system.hold_multiple_seats(seat_ids, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def get_performance_stats() -> Dict[str, Any]:
    """Get performance statistics from global system"""
    if _auto_hold_system:
        return _auto_hold_system.get_performance_stats()
    else:
        return {}

def log_performance_summary():
    """Log performance summary from global system"""
    if _auto_hold_system:
        _auto_hold_system.log_performance_summary()
    else:
        logger.warning("Auto-hold system not initialized")
