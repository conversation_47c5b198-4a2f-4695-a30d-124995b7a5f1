"""
Auto Hold System - Ultra-High Performance Seat Holding
Single optimized system for sub-100ms seat holding with detailed performance benchmarking.
Supports up to 1000 concurrent seats with pre-cached tokens and minimal overhead.
"""

import asyncio
import json
import logging
import time
import threading
import secrets
import socket
import ssl
from typing import Optional, Dict, List, Any, Callable
from collections import deque, defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor
import httpx

# Import existing helper functions
from helper import build_channel_keys, async_make_request
from chart_token_manager import generate_x_signature
from token_retrieval import get_hold_token, get_cached_event_id

async def check_token_time_remaining(hold_token: str, proxy: Optional[str] = None) -> int:
    """
    Check how much time is left on a token (tokens are already active when generated)
    This is just for monitoring, not activation
    """
    try:
        # Use GET instead of POST since we're just checking status
        url = f'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{hold_token}'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = await async_make_request('GET', url, proxy=proxy, headers=headers)

        if response.status_code == 200:
            try:
                data = response.json()
                time_left = data.get('expiresInSeconds', TOKEN_EXPIRY_SECONDS)
                logger.debug(f"Token {hold_token[:8]}... has {time_left} seconds remaining")
                return time_left
            except Exception as e:
                logger.debug(f"Error parsing token status: {e}")
                return TOKEN_EXPIRY_SECONDS
        else:
            logger.debug(f"Token status check failed: {response.status_code}")
            return TOKEN_EXPIRY_SECONDS

    except Exception as e:
        logger.debug(f"Error checking token time: {e}")
        return TOKEN_EXPIRY_SECONDS

logger = logging.getLogger("webook_pro")

# Constants for optimization
SEATSIO_IP = "*************"
HOLD_URL = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
MAX_CONCURRENT_HOLDS = 1000
TOKEN_POOL_SIZE = 10  # Reduced default since each token can hold 50 seats
TOKEN_REFRESH_THRESHOLD = 300  # 5 minutes
SEATS_PER_TOKEN = 50  # Each token can hold up to 50 seats
TOKEN_EXPIRY_SECONDS = 900  # 15 minutes default expiry

class PerformanceTracker:
    """Detailed performance tracking for auto-hold operations"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.lock = threading.RLock()
        
        # Timing metrics (all in milliseconds)
        self.websocket_to_decision = deque(maxlen=max_samples)
        self.decision_to_request = deque(maxlen=max_samples)
        self.request_to_response = deque(maxlen=max_samples)
        self.total_response_times = deque(maxlen=max_samples)
        
        # Success/failure tracking
        self.attempts = 0
        self.successes = 0
        self.failures = 0
        self.token_failures = 0
        self.network_failures = 0
        
        # Current operation tracking
        self.active_operations = {}
        
    def start_operation(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> str:
        """Start tracking a new hold operation"""
        operation_id = f"{seat_id}_{int(time.time() * 1000000)}"
        start_time = time.perf_counter()
        
        with self.lock:
            self.attempts += 1
            self.active_operations[operation_id] = {
                'seat_id': seat_id,
                'start_time': start_time,
                'websocket_timestamp': websocket_timestamp or start_time,
                'decision_time': None,
                'request_time': None,
                'response_time': None
            }
            
        return operation_id
    
    def record_decision(self, operation_id: str):
        """Record when decision to hold was made"""
        decision_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['decision_time'] = decision_time
                
                # Calculate websocket to decision time
                ws_to_decision_ms = (decision_time - op['websocket_timestamp']) * 1000
                self.websocket_to_decision.append(ws_to_decision_ms)
    
    def record_request_start(self, operation_id: str):
        """Record when network request started"""
        request_time = time.perf_counter()
        
        with self.lock:
            if operation_id in self.active_operations:
                op = self.active_operations[operation_id]
                op['request_time'] = request_time
                
                # Calculate decision to request time
                if op['decision_time']:
                    decision_to_req_ms = (request_time - op['decision_time']) * 1000
                    self.decision_to_request.append(decision_to_req_ms)
    
    def complete_operation(self, operation_id: str, success: bool, failure_reason: str = None):
        """Complete an operation and record final metrics"""
        response_time = time.perf_counter()
        
        with self.lock:
            if operation_id not in self.active_operations:
                return
                
            op = self.active_operations[operation_id]
            op['response_time'] = response_time
            
            # Calculate final timings
            if op['request_time']:
                req_to_resp_ms = (response_time - op['request_time']) * 1000
                self.request_to_response.append(req_to_resp_ms)
            
            total_time_ms = (response_time - op['start_time']) * 1000
            self.total_response_times.append(total_time_ms)
            
            # Update counters
            if success:
                self.successes += 1
            else:
                self.failures += 1
                if failure_reason == 'token':
                    self.token_failures += 1
                elif failure_reason == 'network':
                    self.network_failures += 1
            
            # Log detailed performance info
            seat_id = op['seat_id']
            ws_to_decision = (op['decision_time'] - op['websocket_timestamp']) * 1000 if op['decision_time'] else 0
            decision_to_req = (op['request_time'] - op['decision_time']) * 1000 if op['request_time'] and op['decision_time'] else 0
            req_to_resp = req_to_resp_ms if op['request_time'] else 0
            
            status = "✅ SUCCESS" if success else f"❌ FAILED ({failure_reason})"
            logger.info(f"🎯 HOLD PERFORMANCE [{seat_id}] {status}")
            logger.info(f"   📊 WS→Decision: {ws_to_decision:.2f}ms | Decision→Request: {decision_to_req:.2f}ms | Request→Response: {req_to_resp:.2f}ms | TOTAL: {total_time_ms:.2f}ms")
            
            # Clean up
            del self.active_operations[operation_id]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        with self.lock:
            def avg(deque_obj):
                return sum(deque_obj) / len(deque_obj) if deque_obj else 0
            
            return {
                'attempts': self.attempts,
                'successes': self.successes,
                'failures': self.failures,
                'success_rate': (self.successes / self.attempts * 100) if self.attempts > 0 else 0,
                'avg_total_time_ms': avg(self.total_response_times),
                'avg_ws_to_decision_ms': avg(self.websocket_to_decision),
                'avg_decision_to_request_ms': avg(self.decision_to_request),
                'avg_request_to_response_ms': avg(self.request_to_response),
                'token_failures': self.token_failures,
                'network_failures': self.network_failures,
                'active_operations': len(self.active_operations)
            }

class PersistentConnection:
    """Ultra-fast persistent HTTP connection for minimal overhead"""

    def __init__(self, host: str, port: int = 443, use_ssl: bool = True):
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.socket = None
        self.ssl_socket = None
        self.lock = threading.RLock()
        self.connected = False
        self.last_used = time.time()

        # Connect immediately
        self._connect()

    def _connect(self):
        """Establish persistent connection"""
        try:
            # Create raw socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)

            # Connect to host
            self.socket.connect((self.host, self.port))

            if self.use_ssl:
                # Wrap with SSL
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                self.ssl_socket = context.wrap_socket(self.socket, server_hostname=self.host)
                self.active_socket = self.ssl_socket
            else:
                self.active_socket = self.socket

            self.connected = True
            self.last_used = time.time()
            logger.debug(f"🔗 Persistent connection established to {self.host}:{self.port}")

        except Exception as e:
            logger.error(f"❌ Failed to establish persistent connection: {e}")
            self.connected = False
            self._cleanup()

    def send_request(self, method: str, path: str, headers: Dict[str, str], body: str = "") -> tuple:
        """Send HTTP request over persistent connection"""
        with self.lock:
            if not self.connected:
                self._connect()
                if not self.connected:
                    return None, "Connection failed"

            try:
                # Build HTTP request
                request_lines = [f"{method} {path} HTTP/1.1"]

                # Add headers
                for key, value in headers.items():
                    request_lines.append(f"{key}: {value}")

                # Add content length if body
                if body:
                    request_lines.append(f"Content-Length: {len(body.encode('utf-8'))}")

                request_lines.append("")  # Empty line
                if body:
                    request_lines.append(body)

                request_data = "\r\n".join(request_lines).encode('utf-8')

                # Send request
                self.active_socket.sendall(request_data)

                # Read response
                response_data = b""
                while True:
                    chunk = self.active_socket.recv(4096)
                    if not chunk:
                        break
                    response_data += chunk

                    # Check if we have complete response
                    if b"\r\n\r\n" in response_data:
                        # Parse headers to check content length
                        header_end = response_data.find(b"\r\n\r\n")
                        headers_part = response_data[:header_end].decode('utf-8')
                        body_part = response_data[header_end + 4:]

                        # Extract status code
                        status_line = headers_part.split('\r\n')[0]
                        status_code = int(status_line.split()[1])

                        # For 204 No Content, we're done
                        if status_code == 204:
                            break

                        # Check content length
                        content_length = 0
                        for line in headers_part.split('\r\n')[1:]:
                            if line.lower().startswith('content-length:'):
                                content_length = int(line.split(':')[1].strip())
                                break

                        if len(body_part) >= content_length:
                            break

                # Parse response
                if b"\r\n\r\n" in response_data:
                    header_end = response_data.find(b"\r\n\r\n")
                    headers_part = response_data[:header_end].decode('utf-8')
                    body_part = response_data[header_end + 4:].decode('utf-8')

                    status_line = headers_part.split('\r\n')[0]
                    status_code = int(status_line.split()[1])

                    self.last_used = time.time()
                    return status_code, body_part
                else:
                    return None, "Invalid response"

            except Exception as e:
                logger.error(f"❌ Request failed on persistent connection: {e}")
                self.connected = False
                self._cleanup()
                return None, str(e)

    def _cleanup(self):
        """Clean up connection"""
        try:
            if self.ssl_socket:
                self.ssl_socket.close()
            if self.socket:
                self.socket.close()
        except:
            pass
        self.socket = None
        self.ssl_socket = None
        self.active_socket = None

    def close(self):
        """Close connection"""
        with self.lock:
            self.connected = False
            self._cleanup()

class ConnectionPool:
    """Pool of persistent connections for maximum performance"""

    def __init__(self, host: str, pool_size: int = 10):
        self.host = host
        self.pool_size = pool_size
        self.connections = deque()
        self.lock = threading.RLock()

        # Pre-create connections
        for _ in range(pool_size):
            conn = PersistentConnection(host)
            if conn.connected:
                self.connections.append(conn)

        logger.info(f"🔗 Connection pool created: {len(self.connections)}/{pool_size} connections to {host}")

    def get_connection(self) -> Optional[PersistentConnection]:
        """Get a connection from the pool"""
        with self.lock:
            # Try to get a working connection
            for _ in range(len(self.connections)):
                if self.connections:
                    conn = self.connections.popleft()
                    if conn.connected:
                        return conn
                    else:
                        # Try to reconnect
                        conn._connect()
                        if conn.connected:
                            return conn

            # No connections available, create new one
            conn = PersistentConnection(self.host)
            if conn.connected:
                return conn

            return None

    def return_connection(self, conn: PersistentConnection):
        """Return connection to pool"""
        with self.lock:
            if conn.connected and len(self.connections) < self.pool_size:
                self.connections.append(conn)
            else:
                conn.close()

    def cleanup(self):
        """Clean up all connections"""
        with self.lock:
            while self.connections:
                conn = self.connections.popleft()
                conn.close()

class TokenPool:
    """High-performance token pool with pre-cached tokens based on tickets wanted"""

    def __init__(self, initial_pool_size: int = TOKEN_POOL_SIZE):
        self.pool_size = initial_pool_size
        self.tokens = deque()
        self.token_expiry = {}
        self.lock = threading.RLock()
        self.refresh_in_progress = False
        self.target_tickets = 0  # Number of tickets user wants

        # Start background token refresh
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()

    def set_target_tickets(self, tickets_wanted: int):
        """Set the number of tickets wanted to ensure enough tokens"""
        with self.lock:
            self.target_tickets = tickets_wanted
            # Calculate tokens needed: each token can hold 50 seats, add 1 for safety
            tokens_needed = (tickets_wanted + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN + 1
            self.pool_size = max(TOKEN_POOL_SIZE, tokens_needed)
            logger.info(f"🎯 Token pool target set: {tickets_wanted} tickets = {tokens_needed} tokens needed, pool size: {self.pool_size}")
    
    def get_token(self) -> Optional[str]:
        """Get a valid token from the pool"""
        with self.lock:
            # Remove expired tokens
            current_time = time.time()
            while self.tokens:
                token = self.tokens[0]
                # Check if token has enough time left (at least 5 minutes)
                if token in self.token_expiry and self.token_expiry[token] > current_time + TOKEN_REFRESH_THRESHOLD:
                    return self.tokens.popleft()
                else:
                    # Token expired or about to expire, remove it
                    expired_token = self.tokens.popleft()
                    self.token_expiry.pop(expired_token, None)
                    logger.debug(f"Removed expired token {expired_token[:8]}...")

            # No valid tokens available
            logger.warning("⚠️ No valid tokens available in pool")
            return None
    
    def _refresh_loop(self):
        """Background thread to keep token pool filled based on tickets wanted"""
        while True:
            try:
                with self.lock:
                    current_pool_size = self.pool_size
                    needed_tokens = current_pool_size - len(self.tokens)
                    if needed_tokens <= 0 or self.refresh_in_progress:
                        time.sleep(1)
                        continue

                    self.refresh_in_progress = True

                # Get event ID
                event_id = get_cached_event_id()
                if not event_id:
                    with self.lock:
                        self.refresh_in_progress = False
                    time.sleep(5)
                    continue

                # Create tokens asynchronously - batch them for efficiency
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Create tokens in batches of 10 for efficiency
                    batch_size = min(needed_tokens, 10)
                    tasks = [get_hold_token(event_id) for _ in range(batch_size)]
                    new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

                    # Add valid tokens to pool
                    current_time = time.time()
                    added_count = 0

                    with self.lock:
                        for token in new_tokens:
                            if isinstance(token, str) and token:
                                self.tokens.append(token)
                                # Tokens are already active when generated - just set expiry time
                                self.token_expiry[token] = current_time + TOKEN_EXPIRY_SECONDS
                                added_count += 1

                        total_tokens = len(self.tokens)
                        logger.info(f"🔄 Token pool refreshed: +{added_count} tokens, {total_tokens}/{current_pool_size} available")

                        # If we have target tickets set, log progress
                        if self.target_tickets > 0:
                            coverage = (total_tokens / self.target_tickets) * 100
                            logger.info(f"🎯 Token coverage: {coverage:.1f}% for {self.target_tickets} target tickets")

                finally:
                    loop.close()
                    with self.lock:
                        self.refresh_in_progress = False

            except Exception as e:
                logger.error(f"Error refreshing token pool: {e}")
                with self.lock:
                    self.refresh_in_progress = False

            time.sleep(5)  # Check every 5 seconds for faster response

    def force_token_generation(self, count: int) -> int:
        """Force immediate generation of tokens (blocking)"""
        logger.info(f"🔄 Force generating {count} tokens...")

        # Get event ID
        event_id = get_cached_event_id()
        if not event_id:
            logger.error("❌ No event ID available for token generation")
            return 0

        # Generate tokens synchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            tasks = [get_hold_token(event_id) for _ in range(count)]
            new_tokens = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

            # Add valid tokens to pool
            current_time = time.time()
            added_count = 0

            with self.lock:
                for token in new_tokens:
                    if isinstance(token, str) and token:
                        self.tokens.append(token)
                        # Tokens are already active when generated
                        self.token_expiry[token] = current_time + TOKEN_EXPIRY_SECONDS
                        added_count += 1
                        logger.info(f"✅ Generated token {token[:8]}... ({added_count}/{count})")

            logger.info(f"🔄 Force generation complete: {added_count}/{count} tokens created")
            return added_count

        except Exception as e:
            logger.error(f"❌ Error in force token generation: {e}")
            return 0
        finally:
            loop.close()

class AutoHoldSystem:
    """Ultra-high performance auto-hold system"""
    
    def __init__(self, event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None):
        self.event_key = event_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        
        # Performance tracking
        self.performance = PerformanceTracker()

        # Token management
        self.token_pool = TokenPool()

        # Persistent connection pool for MINIMAL overhead
        self.connection_pool = ConnectionPool(SEATSIO_IP, pool_size=MAX_CONCURRENT_HOLDS)

        # Thread pool for concurrent operations
        self.executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_HOLDS, thread_name_prefix="AutoHold")
        
        # Pre-built request template
        self.request_template = {
            'events': [self.event_key],
            'holdToken': None,
            'objects': [{'objectId': None}],
            'channelKeys': build_channel_keys(self.channel_keys, self.team_id),
            'validateEventsLinkedToSameChart': True,
        }
        
        # Pre-built headers template
        self.headers_template = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'x-client-tool': 'Renderer',
            'x-browser-id': None,
            'x-signature': None
        }
        
        # Callbacks
        self.success_callback: Optional[Callable[[str, str], None]] = None
        self.failure_callback: Optional[Callable[[str, str], None]] = None
        
        logger.info(f"🚀 AutoHoldSystem initialized for event {event_key} with {len(self.connection_pool.connections)} persistent connections")

    def set_target_tickets(self, tickets_wanted: int):
        """Set the number of tickets wanted to ensure enough tokens are pre-cached"""
        self.token_pool.set_target_tickets(tickets_wanted)

        # Calculate tokens needed
        tokens_needed = (tickets_wanted + SEATS_PER_TOKEN - 1) // SEATS_PER_TOKEN + 1

        # Check current token count
        current_tokens = len(self.token_pool.tokens)

        if current_tokens < tokens_needed:
            logger.info(f"🔄 Need {tokens_needed} tokens for {tickets_wanted} tickets, have {current_tokens}")
            # Force generate the needed tokens immediately
            needed = tokens_needed - current_tokens
            generated = self.token_pool.force_token_generation(needed)
            logger.info(f"🎯 Auto-hold system configured: {tickets_wanted} tickets, {current_tokens + generated} tokens ready")
        else:
            logger.info(f"🎯 Auto-hold system configured: {tickets_wanted} tickets, {current_tokens} tokens already ready")

    def wait_for_tokens(self, required_tokens: int, timeout: float = 30.0) -> bool:
        """Wait until we have enough tokens available"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            with self.token_pool.lock:
                available_tokens = len(self.token_pool.tokens)
                if available_tokens >= required_tokens:
                    logger.info(f"✅ {available_tokens} tokens ready for {required_tokens} required")
                    return True

            logger.info(f"⏳ Waiting for tokens: {len(self.token_pool.tokens)}/{required_tokens}")
            time.sleep(1)

        logger.warning(f"⚠️ Timeout waiting for tokens: {len(self.token_pool.tokens)}/{required_tokens}")
        return False

    def set_callbacks(self, success_callback: Callable[[str, str], None], failure_callback: Callable[[str, str], None]):
        """Set callbacks for success and failure events"""
        self.success_callback = success_callback
        self.failure_callback = failure_callback

    def hold_seat(self, seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
        """
        Ultra-fast seat holding - main entry point
        Returns immediately, actual hold happens asynchronously
        """
        # Start performance tracking
        operation_id = self.performance.start_operation(seat_id, websocket_timestamp)

        # Record decision time (immediate)
        self.performance.record_decision(operation_id)

        # Submit to thread pool for immediate return
        self.executor.submit(self._hold_seat_sync, seat_id, operation_id)

        return True

    def _hold_seat_sync(self, seat_id: str, operation_id: str):
        """Ultra-fast seat holding with persistent connections - MINIMAL OVERHEAD"""
        connection = None
        token = None

        try:
            # Get token from pool (pre-cached, instant)
            token = self.token_pool.get_token()
            if not token:
                self.performance.complete_operation(operation_id, False, 'token')
                if self.failure_callback:
                    self.failure_callback(seat_id, "No tokens available")
                return False

            # Get persistent connection (already established)
            connection = self.connection_pool.get_connection()
            if not connection:
                self.performance.complete_operation(operation_id, False, 'connection')
                if self.failure_callback:
                    self.failure_callback(seat_id, "No connections available")
                return False

            # Build request data (minimal overhead)
            request_data = self.request_template.copy()
            request_data['holdToken'] = token
            request_data['objects'] = [{'objectId': seat_id}]

            # Build headers (minimal overhead)
            headers = self.headers_template.copy()
            headers['x-browser-id'] = secrets.token_hex(8)

            # Serialize and sign (minimal overhead)
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['x-signature'] = generate_x_signature(body_str)

            # Record request start
            self.performance.record_request_start(operation_id)

            # CRITICAL: Send over persistent connection (MINIMAL OVERHEAD)
            path = '/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
            status_code, response_body = connection.send_request('POST', path, headers, body_str)

            # Process response
            if status_code == 204:
                self.performance.complete_operation(operation_id, True)
                if self.success_callback:
                    self.success_callback(seat_id, token)
                return True
            else:
                error_msg = f"HTTP {status_code}: {response_body[:100] if response_body else 'No response'}"
                self.performance.complete_operation(operation_id, False, 'network')
                if self.failure_callback:
                    self.failure_callback(seat_id, error_msg)
                return False

        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            self.performance.complete_operation(operation_id, False, 'network')
            if self.failure_callback:
                self.failure_callback(seat_id, error_msg)
            return False

        finally:
            if connection:
                self.connection_pool.return_connection(connection)

    def hold_multiple_seats(self, seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
        """
        Hold multiple seats concurrently for maximum speed
        """
        if len(seat_ids) > MAX_CONCURRENT_HOLDS:
            logger.warning(f"Requested {len(seat_ids)} seats, limiting to {MAX_CONCURRENT_HOLDS}")
            seat_ids = seat_ids[:MAX_CONCURRENT_HOLDS]

        # Submit all holds concurrently
        for seat_id in seat_ids:
            self.hold_seat(seat_id, websocket_timestamp)

        logger.info(f"🎯 Submitted {len(seat_ids)} concurrent hold operations")
        return True

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics"""
        return self.performance.get_stats()

    def log_performance_summary(self):
        """Log a detailed performance summary"""
        stats = self.get_performance_stats()

        logger.info("📊 AUTO-HOLD PERFORMANCE SUMMARY")
        logger.info(f"   🎯 Total Attempts: {stats['attempts']}")
        logger.info(f"   ✅ Successes: {stats['successes']} ({stats['success_rate']:.1f}%)")
        logger.info(f"   ❌ Failures: {stats['failures']} (Token: {stats['token_failures']}, Network: {stats['network_failures']})")
        logger.info(f"   ⏱️  Average Total Time: {stats['avg_total_time_ms']:.2f}ms")
        logger.info(f"   📡 WebSocket→Decision: {stats['avg_ws_to_decision_ms']:.2f}ms")
        logger.info(f"   🔄 Decision→Request: {stats['avg_decision_to_request_ms']:.2f}ms")
        logger.info(f"   🌐 Request→Response: {stats['avg_request_to_response_ms']:.2f}ms")
        logger.info(f"   🔄 Active Operations: {stats['active_operations']}")

    def cleanup(self):
        """Clean up resources"""
        self.executor.shutdown(wait=True)
        self.connection_pool.cleanup()
        logger.info("🧹 AutoHoldSystem cleaned up")

# Global instance for easy access
_auto_hold_system: Optional[AutoHoldSystem] = None

def initialize_auto_hold(event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None) -> AutoHoldSystem:
    """Initialize the global auto-hold system"""
    global _auto_hold_system

    if _auto_hold_system:
        _auto_hold_system.cleanup()

    _auto_hold_system = AutoHoldSystem(event_key, channel_keys, team_id, proxy)
    return _auto_hold_system

def get_auto_hold_system() -> Optional[AutoHoldSystem]:
    """Get the global auto-hold system instance"""
    return _auto_hold_system

def hold_seat_fast(seat_id: str, websocket_timestamp: Optional[float] = None) -> bool:
    """Fast seat holding using global system"""
    if _auto_hold_system:
        return _auto_hold_system.hold_seat(seat_id, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def hold_multiple_seats_fast(seat_ids: List[str], websocket_timestamp: Optional[float] = None) -> bool:
    """Fast multiple seat holding using global system"""
    if _auto_hold_system:
        return _auto_hold_system.hold_multiple_seats(seat_ids, websocket_timestamp)
    else:
        logger.error("Auto-hold system not initialized")
        return False

def get_performance_stats() -> Dict[str, Any]:
    """Get performance statistics from global system"""
    if _auto_hold_system:
        return _auto_hold_system.get_performance_stats()
    else:
        return {}

def log_performance_summary():
    """Log performance summary from global system"""
    if _auto_hold_system:
        _auto_hold_system.log_performance_summary()
    else:
        logger.warning("Auto-hold system not initialized")
