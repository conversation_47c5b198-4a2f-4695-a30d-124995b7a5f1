"""
Ultra-Fast Non-HTTP Protocols for Seat Holding
Explores alternatives to HTTP/SSL for maximum speed
"""

import asyncio
import json
import logging
import socket
import struct
import time
import threading
import websockets
from typing import Optional, Dict, Any, Callable
from collections import deque

logger = logging.getLogger("webook_pro")

class WebSocketHoldClient:
    """
    Option 1: Use existing WebSocket connection for bidirectional communication
    This would be the fastest since connection is already established
    """
    
    def __init__(self, websocket_uri: str, event_key: str):
        self.websocket_uri = websocket_uri
        self.event_key = event_key
        self.websocket = None
        self.connected = False
        self.response_callbacks = {}
        self.request_id = 0
        self.lock = threading.Lock()
        
    async def connect(self):
        """Connect to WebSocket"""
        try:
            self.websocket = await websockets.connect(self.websocket_uri)
            self.connected = True
            
            # Start listening for responses
            asyncio.create_task(self._listen_for_responses())
            
            logger.info("🔗 WebSocket hold client connected")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect WebSocket hold client: {e}")
            return False
    
    async def _listen_for_responses(self):
        """Listen for hold responses"""
        try:
            while self.connected:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                # Check if this is a hold response
                if data.get('type') == 'HOLD_RESPONSE':
                    request_id = data.get('request_id')
                    if request_id in self.response_callbacks:
                        callback = self.response_callbacks.pop(request_id)
                        callback(data)
                        
        except Exception as e:
            logger.error(f"❌ WebSocket listener error: {e}")
            self.connected = False
    
    async def hold_seat(self, seat_id: str, token: str, callback: Callable) -> bool:
        """Send hold request via WebSocket"""
        if not self.connected:
            return False
        
        with self.lock:
            self.request_id += 1
            request_id = self.request_id
        
        # Store callback for response
        self.response_callbacks[request_id] = callback
        
        # Send hold request
        hold_request = {
            'type': 'HOLD_REQUEST',
            'request_id': request_id,
            'data': {
                'seat_id': seat_id,
                'token': token,
                'event_key': self.event_key,
                'timestamp': time.time()
            }
        }
        
        try:
            await self.websocket.send(json.dumps(hold_request))
            return True
        except Exception as e:
            logger.error(f"❌ Failed to send WebSocket hold request: {e}")
            self.response_callbacks.pop(request_id, None)
            return False

class UDPHoldClient:
    """
    Option 2: Ultra-fast UDP client for hold requests
    No connection overhead, maximum speed, but no delivery guarantee
    """
    
    def __init__(self, server_host: str, server_port: int):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.socket.settimeout(1.0)  # 1 second timeout for responses
        
    def hold_seat(self, seat_id: str, token: str, event_key: str) -> tuple:
        """Send UDP hold request - ULTRA FAST"""
        try:
            # Create binary packet for maximum speed
            packet = self._create_hold_packet(seat_id, token, event_key)
            
            # Send UDP packet (no connection overhead)
            start_time = time.perf_counter()
            self.socket.sendto(packet, (self.server_host, self.server_port))
            
            # Try to receive response (optional)
            try:
                response_data, addr = self.socket.recvfrom(1024)
                response_time = (time.perf_counter() - start_time) * 1000
                
                # Parse response
                success = self._parse_response(response_data)
                return success, response_time
                
            except socket.timeout:
                # No response received, assume success for speed
                response_time = (time.perf_counter() - start_time) * 1000
                return True, response_time
                
        except Exception as e:
            logger.error(f"❌ UDP hold request failed: {e}")
            return False, 0
    
    def _create_hold_packet(self, seat_id: str, token: str, event_key: str) -> bytes:
        """Create binary packet for hold request"""
        # Binary format for maximum speed:
        # [4 bytes: packet type] [4 bytes: timestamp] [variable: seat_id] [variable: token] [variable: event_key]
        
        packet_type = 1  # Hold request
        timestamp = int(time.time())
        
        # Encode strings
        seat_id_bytes = seat_id.encode('utf-8')
        token_bytes = token.encode('utf-8')
        event_key_bytes = event_key.encode('utf-8')
        
        # Pack binary data
        packet = struct.pack(
            f'!II{len(seat_id_bytes)}s{len(token_bytes)}s{len(event_key_bytes)}s',
            packet_type,
            timestamp,
            seat_id_bytes,
            token_bytes,
            event_key_bytes
        )
        
        return packet
    
    def _parse_response(self, data: bytes) -> bool:
        """Parse binary response"""
        try:
            # Simple response: [4 bytes: status code]
            status_code = struct.unpack('!I', data[:4])[0]
            return status_code == 200  # Success
        except:
            return False
    
    def close(self):
        """Close UDP socket"""
        self.socket.close()

class RawTCPHoldClient:
    """
    Option 3: Raw TCP with custom binary protocol
    Faster than HTTP but with connection reliability
    """
    
    def __init__(self, server_host: str, server_port: int):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
        self.connected = False
        self.lock = threading.Lock()
        
    def connect(self) -> bool:
        """Establish raw TCP connection"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)
            self.socket.connect((self.server_host, self.server_port))
            self.connected = True
            
            logger.info(f"🔗 Raw TCP connection established to {self.server_host}:{self.server_port}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to establish TCP connection: {e}")
            return False
    
    def hold_seat(self, seat_id: str, token: str, event_key: str) -> tuple:
        """Send hold request over raw TCP"""
        with self.lock:
            if not self.connected:
                if not self.connect():
                    return False, 0
            
            try:
                # Create binary message
                message = self._create_hold_message(seat_id, token, event_key)
                
                # Send message
                start_time = time.perf_counter()
                self.socket.sendall(message)
                
                # Receive response
                response = self.socket.recv(1024)
                response_time = (time.perf_counter() - start_time) * 1000
                
                # Parse response
                success = self._parse_tcp_response(response)
                return success, response_time
                
            except Exception as e:
                logger.error(f"❌ TCP hold request failed: {e}")
                self.connected = False
                return False, 0
    
    def _create_hold_message(self, seat_id: str, token: str, event_key: str) -> bytes:
        """Create binary message for TCP"""
        # Message format: [4 bytes: message length] [4 bytes: command] [data]
        
        command = 1  # Hold command
        data = {
            'seat_id': seat_id,
            'token': token,
            'event_key': event_key,
            'timestamp': time.time()
        }
        
        # Serialize data
        data_bytes = json.dumps(data).encode('utf-8')
        message_length = 4 + len(data_bytes)  # command + data
        
        # Pack message
        message = struct.pack('!II', message_length, command) + data_bytes
        return message
    
    def _parse_tcp_response(self, data: bytes) -> bool:
        """Parse TCP response"""
        try:
            if len(data) >= 4:
                status = struct.unpack('!I', data[:4])[0]
                return status == 200
            return False
        except:
            return False
    
    def close(self):
        """Close TCP connection"""
        if self.socket:
            self.socket.close()
            self.connected = False

class MemoryMappedHoldService:
    """
    Option 4: Memory-mapped file for local ultra-fast communication
    Only works if hold service runs locally
    """
    
    def __init__(self, mmap_file: str, max_size: int = 1024 * 1024):
        self.mmap_file = mmap_file
        self.max_size = max_size
        self.mmap = None
        self.lock = threading.Lock()
        
    def initialize(self) -> bool:
        """Initialize memory-mapped file"""
        try:
            import mmap
            
            # Create/open file
            with open(self.mmap_file, 'wb') as f:
                f.write(b'\x00' * self.max_size)
            
            # Memory map the file
            with open(self.mmap_file, 'r+b') as f:
                self.mmap = mmap.mmap(f.fileno(), self.max_size)
            
            logger.info(f"🗂️ Memory-mapped hold service initialized: {self.mmap_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize memory-mapped service: {e}")
            return False
    
    def hold_seat(self, seat_id: str, token: str, event_key: str) -> tuple:
        """Write hold request to memory-mapped file - INSTANT"""
        if not self.mmap:
            return False, 0
        
        with self.lock:
            try:
                start_time = time.perf_counter()
                
                # Create hold request
                request = {
                    'command': 'hold',
                    'seat_id': seat_id,
                    'token': token,
                    'event_key': event_key,
                    'timestamp': time.time()
                }
                
                # Write to memory-mapped file
                request_bytes = json.dumps(request).encode('utf-8')
                self.mmap.seek(0)
                self.mmap.write(request_bytes)
                self.mmap.flush()
                
                response_time = (time.perf_counter() - start_time) * 1000
                
                # In a real implementation, the service would read from the file
                # and write back a response. For now, assume success.
                return True, response_time
                
            except Exception as e:
                logger.error(f"❌ Memory-mapped hold failed: {e}")
                return False, 0
    
    def close(self):
        """Close memory-mapped file"""
        if self.mmap:
            self.mmap.close()

# Performance comparison function
async def compare_protocols():
    """Compare performance of different protocols"""
    
    logger.info("🚀 PROTOCOL PERFORMANCE COMPARISON")
    logger.info("=" * 50)
    
    # Test data
    seat_id = "A-1"
    token = "test_token_12345"
    event_key = "test_event"
    
    results = {}
    
    # Test 1: WebSocket (if available)
    try:
        ws_client = WebSocketHoldClient("wss://messaging-eu.seatsio.net/ws", event_key)
        if await ws_client.connect():
            
            response_received = False
            response_time = 0
            
            def ws_callback(response):
                nonlocal response_received, response_time
                response_received = True
                response_time = (time.time() - start_time) * 1000
            
            start_time = time.time()
            await ws_client.hold_seat(seat_id, token, ws_callback)
            
            # Wait for response
            await asyncio.sleep(0.1)
            
            results['WebSocket'] = response_time if response_received else 'No response'
            logger.info(f"📡 WebSocket: {results['WebSocket']}")
        
    except Exception as e:
        results['WebSocket'] = f'Error: {e}'
        logger.info(f"📡 WebSocket: {results['WebSocket']}")
    
    # Test 2: UDP
    try:
        udp_client = UDPHoldClient("34.102.211.90", 8080)  # Hypothetical UDP port
        success, udp_time = udp_client.hold_seat(seat_id, token, event_key)
        results['UDP'] = f'{udp_time:.2f}ms' if success else 'Failed'
        udp_client.close()
        logger.info(f"📦 UDP: {results['UDP']}")
        
    except Exception as e:
        results['UDP'] = f'Error: {e}'
        logger.info(f"📦 UDP: {results['UDP']}")
    
    # Test 3: Raw TCP
    try:
        tcp_client = RawTCPHoldClient("34.102.211.90", 8081)  # Hypothetical TCP port
        success, tcp_time = tcp_client.hold_seat(seat_id, token, event_key)
        results['Raw TCP'] = f'{tcp_time:.2f}ms' if success else 'Failed'
        tcp_client.close()
        logger.info(f"🔌 Raw TCP: {results['Raw TCP']}")
        
    except Exception as e:
        results['Raw TCP'] = f'Error: {e}'
        logger.info(f"🔌 Raw TCP: {results['Raw TCP']}")
    
    # Test 4: Memory-mapped (local only)
    try:
        mmap_service = MemoryMappedHoldService("/tmp/hold_service.mmap")
        if mmap_service.initialize():
            success, mmap_time = mmap_service.hold_seat(seat_id, token, event_key)
            results['Memory-mapped'] = f'{mmap_time:.4f}ms' if success else 'Failed'
            mmap_service.close()
        logger.info(f"🗂️ Memory-mapped: {results['Memory-mapped']}")
        
    except Exception as e:
        results['Memory-mapped'] = f'Error: {e}'
        logger.info(f"🗂️ Memory-mapped: {results['Memory-mapped']}")
    
    # Summary
    logger.info("\n📊 PERFORMANCE SUMMARY:")
    for protocol, result in results.items():
        logger.info(f"   {protocol}: {result}")
    
    return results

if __name__ == "__main__":
    # Run comparison
    asyncio.run(compare_protocols())
