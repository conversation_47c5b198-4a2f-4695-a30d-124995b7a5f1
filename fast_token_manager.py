"""
Fast Token Manager - Instant Response to UI Changes
Responds to number_of_seats changes within 100ms, completes generation within 1 second
"""

import logging
import time
import threading
from typing import Optional, Callable
from auto_hold import initialize_auto_hold, get_auto_hold_system

logger = logging.getLogger("webook_pro")

class FastTokenManager:
    """Ultra-fast token manager that responds instantly to UI changes"""
    
    def __init__(self):
        self.auto_hold_system = None
        self.last_update_time = 0
        self.update_lock = threading.Lock()
        self.status_callback: Optional[Callable] = None
        
        # Status monitoring
        self.monitor_thread = threading.Thread(target=self._monitor_status, daemon=True)
        self.monitor_running = True
        self.monitor_thread.start()
    
    def initialize(self, event_key: str, channel_keys: list, team_id: Optional[str] = None, proxy: Optional[str] = None):
        """Initialize the auto-hold system"""
        try:
            self.auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)
            
            if self.auto_hold_system:
                logger.info("🚀 Fast token manager initialized")
                return True
            else:
                logger.error("❌ Failed to initialize auto-hold system")
                return False
                
        except Exception as e:
            logger.error(f"❌ Fast token manager initialization error: {e}")
            return False
    
    def on_seats_changed(self, number_of_seats: int):
        """
        Called when number_of_seats input changes - MUST complete in <100ms
        """
        start_time = time.perf_counter()
        
        with self.update_lock:
            self.last_update_time = time.time()
            
            if not self.auto_hold_system:
                logger.warning("⚠️ Auto-hold system not initialized")
                return
            
            # This call returns immediately (<1ms), generation happens in background
            self.auto_hold_system.set_target_tickets(number_of_seats)
            
            # Get immediate status
            status = self.auto_hold_system.get_token_status()
            
            response_time = (time.perf_counter() - start_time) * 1000
            
            logger.info(f"⚡ INSTANT RESPONSE: {number_of_seats} seats → {response_time:.2f}ms")
            logger.info(f"🔑 Status: {status['available_tokens']}/{status['target_tokens']} tokens, "
                       f"Generation: {'🔄' if status['generation_in_progress'] else '✅'}")
            
            # Notify UI if callback is set
            if self.status_callback:
                self.status_callback(status)
    
    def set_status_callback(self, callback: Callable):
        """Set callback for status updates"""
        self.status_callback = callback
    
    def get_current_status(self) -> dict:
        """Get current token status"""
        if self.auto_hold_system:
            return self.auto_hold_system.get_token_status()
        else:
            return {
                'available_tokens': 0,
                'target_tokens': 0,
                'target_tickets': 0,
                'ready': False,
                'generation_in_progress': False
            }
    
    def _monitor_status(self):
        """Background thread to monitor token generation progress"""
        while self.monitor_running:
            try:
                if self.auto_hold_system:
                    status = self.auto_hold_system.get_token_status()
                    
                    # Log progress if generation is happening
                    if status['generation_in_progress']:
                        logger.info(f"🔄 Generating tokens: {status['available_tokens']}/{status['target_tokens']}")
                    elif status['ready'] and status['target_tickets'] > 0:
                        logger.info(f"✅ Ready: {status['available_tokens']} tokens for {status['target_tickets']} tickets")
                    
                    # Notify UI of status changes
                    if self.status_callback:
                        self.status_callback(status)
                
                time.sleep(0.5)  # Check every 500ms
                
            except Exception as e:
                logger.error(f"❌ Status monitor error: {e}")
                time.sleep(1)
    
    def cleanup(self):
        """Clean up resources"""
        self.monitor_running = False
        if self.auto_hold_system:
            self.auto_hold_system.cleanup()

# Global instance for easy access
_fast_token_manager: Optional[FastTokenManager] = None

def get_fast_token_manager() -> Optional[FastTokenManager]:
    """Get the global fast token manager"""
    return _fast_token_manager

def initialize_fast_token_manager(event_key: str, channel_keys: list, team_id: Optional[str] = None, proxy: Optional[str] = None) -> FastTokenManager:
    """Initialize the global fast token manager"""
    global _fast_token_manager
    
    if _fast_token_manager:
        _fast_token_manager.cleanup()
    
    _fast_token_manager = FastTokenManager()
    success = _fast_token_manager.initialize(event_key, channel_keys, team_id, proxy)
    
    if success:
        logger.info("🚀 Fast token manager ready for instant responses")
    else:
        logger.error("❌ Fast token manager initialization failed")
    
    return _fast_token_manager

def on_seats_input_changed(number_of_seats: int):
    """Call this when the number_of_seats input changes"""
    if _fast_token_manager:
        _fast_token_manager.on_seats_changed(number_of_seats)
    else:
        logger.warning("⚠️ Fast token manager not initialized")

def get_token_status() -> dict:
    """Get current token status"""
    if _fast_token_manager:
        return _fast_token_manager.get_current_status()
    else:
        return {'available_tokens': 0, 'target_tokens': 0, 'ready': False}

# Example integration with Qt/UI
class QtIntegration:
    """Example integration with Qt spinbox"""
    
    def __init__(self, spinbox, status_label):
        self.spinbox = spinbox
        self.status_label = status_label
        
        # Connect spinbox change to fast token manager
        self.spinbox.valueChanged.connect(self.on_spinbox_changed)
        
        # Set up status callback
        if _fast_token_manager:
            _fast_token_manager.set_status_callback(self.update_status_label)
    
    def on_spinbox_changed(self, value):
        """Called when spinbox value changes"""
        start_time = time.perf_counter()
        
        # This should complete in <100ms
        on_seats_input_changed(value)
        
        response_time = (time.perf_counter() - start_time) * 1000
        logger.info(f"⚡ UI Response: {response_time:.2f}ms")
    
    def update_status_label(self, status):
        """Update UI status label"""
        if status['ready']:
            text = f"✅ {status['available_tokens']} tokens ready"
            color = "green"
        elif status['generation_in_progress']:
            text = f"🔄 Generating... {status['available_tokens']}/{status['target_tokens']}"
            color = "orange"
        else:
            text = f"⏳ {status['available_tokens']}/{status['target_tokens']} tokens"
            color = "red"
        
        self.status_label.setText(text)
        self.status_label.setStyleSheet(f"color: {color};")

# Performance test
def test_response_time():
    """Test the response time of the fast token manager"""
    
    print("⚡ TESTING FAST TOKEN MANAGER RESPONSE TIME")
    print("=" * 50)
    
    # Simulate rapid UI changes
    test_values = [10, 50, 100, 200, 500, 100, 50, 200]
    
    for i, seats in enumerate(test_values):
        start_time = time.perf_counter()
        
        # Simulate UI change
        on_seats_input_changed(seats)
        
        response_time = (time.perf_counter() - start_time) * 1000
        
        status = "✅" if response_time < 100 else "❌"
        print(f"Test {i+1}: {seats} seats → {response_time:.2f}ms {status}")
        
        time.sleep(0.1)  # Small delay between tests
    
    print()
    print("🎯 TARGET: <100ms response time")
    print("🚀 GOAL: Complete generation within 1 second")

if __name__ == "__main__":
    print("🚀 Fast Token Manager")
    print("This system responds to UI changes within 100ms")
    print("and completes token generation within 1 second.")
    print()
    print("Integration example:")
    print("1. Initialize: initialize_fast_token_manager(event_key, channel_keys)")
    print("2. Connect UI: spinbox.valueChanged.connect(on_seats_input_changed)")
    print("3. Enjoy instant responses!")
