#!/usr/bin/env python3
"""
Tickets Wanted Auto Hold System
Optimized system that pre-caches tokens based on tickets wanted and holds concurrently
"""

import asyncio
import logging
import time
from typing import List, Optional

from auto_hold import initialize_auto_hold, get_auto_hold_system, hold_multiple_seats_fast
from token_retrieval import cache_event_id, get_cached_event_id

logger = logging.getLogger("webook_pro")

class TicketsWantedAutoHold:
    """Auto-hold system optimized for specific number of tickets wanted"""
    
    def __init__(self):
        self.auto_hold_system = None
        self.tickets_wanted = 0
        self.held_seats = []
        self.failed_seats = []
        self.target_seats = []
        
    def initialize(self, event_key: str, channel_keys: List[str], tickets_wanted: int, 
                   team_id: Optional[str] = None, proxy: Optional[str] = None):
        """Initialize the system with specific number of tickets wanted"""
        
        self.tickets_wanted = tickets_wanted
        logger.info(f"🎯 Initializing auto-hold for {tickets_wanted} tickets")
        
        # Initialize auto-hold system
        self.auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)
        
        # Set target tickets to pre-cache enough tokens
        self.auto_hold_system.set_target_tickets(tickets_wanted)
        
        # Set up callbacks
        self.auto_hold_system.set_callbacks(
            success_callback=self._on_seat_held,
            failure_callback=self._on_seat_failed
        )
        
        # Wait for enough tokens to be ready
        logger.info(f"⏳ Waiting for {tickets_wanted} tokens to be ready...")
        if self.auto_hold_system.wait_for_tokens(tickets_wanted, timeout=60):
            logger.info(f"✅ {tickets_wanted} tokens ready for auto-hold")
            return True
        else:
            logger.error(f"❌ Failed to get {tickets_wanted} tokens ready")
            return False
    
    def _on_seat_held(self, seat_id: str, token: str):
        """Callback when seat is successfully held"""
        self.held_seats.append(seat_id)
        logger.info(f"✅ Held seat {seat_id} ({len(self.held_seats)}/{self.tickets_wanted})")
        
        # Check if we've reached our target
        if len(self.held_seats) >= self.tickets_wanted:
            logger.info(f"🎉 TARGET REACHED: {len(self.held_seats)} seats held!")
    
    def _on_seat_failed(self, seat_id: str, error: str):
        """Callback when seat hold fails"""
        self.failed_seats.append((seat_id, error))
        logger.error(f"❌ Failed to hold seat {seat_id}: {error}")
    
    def set_target_seats(self, seat_ids: List[str]):
        """Set the specific seats to target"""
        self.target_seats = seat_ids[:self.tickets_wanted]  # Limit to tickets wanted
        logger.info(f"🎯 Target seats set: {len(self.target_seats)} seats")
    
    def hold_all_target_seats(self) -> bool:
        """Hold all target seats concurrently"""
        if not self.target_seats:
            logger.error("❌ No target seats set")
            return False
        
        if not self.auto_hold_system:
            logger.error("❌ Auto-hold system not initialized")
            return False
        
        # Check if we have enough tokens
        required_tokens = len(self.target_seats)
        if not self.auto_hold_system.wait_for_tokens(required_tokens, timeout=10):
            logger.warning(f"⚠️ Only {len(self.auto_hold_system.token_pool.tokens)} tokens available for {required_tokens} seats")
        
        # Hold all seats concurrently
        logger.info(f"🚀 Holding {len(self.target_seats)} seats concurrently...")
        start_time = time.perf_counter()
        
        success = hold_multiple_seats_fast(self.target_seats)
        
        submission_time = (time.perf_counter() - start_time) * 1000
        logger.info(f"📊 Concurrent submission completed in {submission_time:.2f}ms")
        
        return success
    
    def hold_seats_from_websocket(self, available_seats: List[str]) -> bool:
        """Hold seats as they become available from websocket updates"""
        if len(self.held_seats) >= self.tickets_wanted:
            return True  # Already have enough seats
        
        # Calculate how many more seats we need
        seats_needed = self.tickets_wanted - len(self.held_seats)
        seats_to_hold = available_seats[:seats_needed]
        
        if not seats_to_hold:
            return True
        
        logger.info(f"🎯 Holding {len(seats_to_hold)} seats from websocket updates")
        return hold_multiple_seats_fast(seats_to_hold)
    
    def get_status(self) -> dict:
        """Get current status"""
        return {
            'tickets_wanted': self.tickets_wanted,
            'seats_held': len(self.held_seats),
            'seats_failed': len(self.failed_seats),
            'seats_remaining': max(0, self.tickets_wanted - len(self.held_seats)),
            'success_rate': (len(self.held_seats) / max(1, len(self.held_seats) + len(self.failed_seats))) * 100,
            'target_reached': len(self.held_seats) >= self.tickets_wanted
        }
    
    def log_status(self):
        """Log current status"""
        status = self.get_status()
        logger.info(f"📊 STATUS: {status['seats_held']}/{status['tickets_wanted']} held, "
                   f"{status['seats_remaining']} remaining, "
                   f"{status['success_rate']:.1f}% success rate")
        
        if self.auto_hold_system:
            self.auto_hold_system.log_performance_summary()
    
    def cleanup(self):
        """Clean up resources"""
        if self.auto_hold_system:
            self.auto_hold_system.cleanup()

# Example usage function
async def example_usage():
    """Example of how to use the tickets wanted auto-hold system"""
    
    # Initialize system for 5 tickets
    auto_hold = TicketsWantedAutoHold()
    
    success = auto_hold.initialize(
        event_key="your_event_key",
        channel_keys=["your_channel_key"],
        tickets_wanted=5,  # Want 5 tickets
        team_id="your_team_id"
    )
    
    if not success:
        logger.error("Failed to initialize auto-hold system")
        return
    
    # Option 1: Hold specific target seats
    target_seats = ["A-1", "A-2", "A-3", "A-4", "A-5"]
    auto_hold.set_target_seats(target_seats)
    auto_hold.hold_all_target_seats()
    
    # Wait for results
    await asyncio.sleep(5)
    
    # Option 2: Hold seats as they become available
    # This would be called from your websocket handler
    available_seats = ["B-1", "B-2", "B-3"]
    auto_hold.hold_seats_from_websocket(available_seats)
    
    # Wait for more results
    await asyncio.sleep(5)
    
    # Check status
    auto_hold.log_status()
    
    # Cleanup
    auto_hold.cleanup()

# Integration with main window
def integrate_with_main_window_tickets(main_window, tickets_wanted: int):
    """
    Integration function for main_window.py with tickets wanted
    """
    
    # Create auto-hold instance
    auto_hold = TicketsWantedAutoHold()
    
    # Store in main window
    main_window.tickets_auto_hold = auto_hold
    
    # Initialize when event loads
    def on_event_loaded(event_key, channel_keys, team_id, proxy=None):
        success = auto_hold.initialize(event_key, channel_keys, tickets_wanted, team_id, proxy)
        if success:
            main_window.log(f"🎯 Auto-hold ready for {tickets_wanted} tickets")
        else:
            main_window.log(f"❌ Failed to initialize auto-hold for {tickets_wanted} tickets")
    
    # Handle websocket updates
    def on_seat_available(seat_id: str):
        """Call this when a seat becomes available"""
        if len(auto_hold.held_seats) < tickets_wanted:
            auto_hold.hold_seats_from_websocket([seat_id])
    
    # Add status logging
    def log_auto_hold_status():
        auto_hold.log_status()
    
    # Add methods to main window
    main_window.initialize_tickets_auto_hold = on_event_loaded
    main_window.on_seat_available = on_seat_available
    main_window.log_auto_hold_status = log_auto_hold_status
    
    return auto_hold

if __name__ == "__main__":
    print("🎯 Tickets Wanted Auto-Hold System")
    print("This system pre-caches tokens based on tickets wanted and holds concurrently.")
    print("Import and use integrate_with_main_window_tickets() to integrate with your main window.")
    
    # Uncomment to run example
    # asyncio.run(example_usage())
