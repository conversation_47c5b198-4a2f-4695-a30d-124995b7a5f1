"""
Lightning Performance Test - Validate sub-50ms performance
Tests the lightning-fast hold manager to ensure it eliminates the 800ms delay
"""

import asyncio
import logging
import time
import statistics
from typing import List, Dict, Any, Optional
from lightning_fast_hold_manager import LightningFastHoldManager
from token_retrieval import get_hold_token, get_cached_event_id

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("lightning_test")


class LightningPerformanceTest:
    """Test system to validate lightning-fast performance"""
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: Optional[str] = None, proxy: Optional[str] = None, event_id: Optional[str] = None):
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
        
        # Test results
        self.test_results = {
            'total_tests': 0,
            'successful_dispatches': 0,
            'failed_dispatches': 0,
            'dispatch_times': [],
            'under_10ms_count': 0,
            'under_25ms_count': 0,
            'under_50ms_count': 0,
            'over_50ms_count': 0
        }
    
    async def run_lightning_test(self, num_tests: int = 100) -> Dict[str, Any]:
        """Run lightning performance test"""
        logger.info(f"⚡ Starting Lightning Performance Test with {num_tests} iterations")
        
        # Initialize the lightning-fast hold manager
        hold_manager = LightningFastHoldManager(
            event_key=self.event_key,
            chart_key=self.chart_key,
            channel_keys=self.channel_keys,
            team_id=self.team_id,
            proxy=self.proxy,
            event_id=self.event_id
        )
        
        # Wait for initial token
        await asyncio.sleep(3.0)
        
        # Test seat ID
        test_seat = "Silver B8-P-16"
        
        logger.info(f"🎯 Running {num_tests} lightning dispatch tests...")
        
        # Run tests
        for i in range(num_tests):
            try:
                # Measure pure dispatch time (not network time)
                start_time = time.perf_counter()
                
                # Test the lightning-fast dispatch
                success = hold_manager.hold_seat_lightning_fast(f"{test_seat}-{i}")
                
                dispatch_time = (time.perf_counter() - start_time) * 1000.0
                
                # Record results
                self.test_results['total_tests'] += 1
                self.test_results['dispatch_times'].append(dispatch_time)
                
                if success:
                    self.test_results['successful_dispatches'] += 1
                else:
                    self.test_results['failed_dispatches'] += 1
                
                # Categorize dispatch times
                if dispatch_time < 10.0:
                    self.test_results['under_10ms_count'] += 1
                elif dispatch_time < 25.0:
                    self.test_results['under_25ms_count'] += 1
                elif dispatch_time < 50.0:
                    self.test_results['under_50ms_count'] += 1
                else:
                    self.test_results['over_50ms_count'] += 1
                
                # Log progress every 20 tests
                if (i + 1) % 20 == 0:
                    avg_time = statistics.mean(self.test_results['dispatch_times'][-20:])
                    logger.info(f"   Progress {i+1}/{num_tests}: Avg dispatch time: {avg_time:.2f}ms")
                
                # Minimal delay between tests
                await asyncio.sleep(0.001)
                
            except Exception as e:
                logger.error(f"❌ Test {i+1} failed: {str(e)}")
                self.test_results['failed_dispatches'] += 1
                self.test_results['total_tests'] += 1
                self.test_results['dispatch_times'].append(100.0)  # Penalty time
                self.test_results['over_50ms_count'] += 1
        
        # Wait for any pending operations
        await asyncio.sleep(2.0)
        
        # Calculate final statistics
        results = self._calculate_lightning_results(hold_manager)
        
        # Cleanup
        hold_manager.cleanup()
        
        return results
    
    def _calculate_lightning_results(self, hold_manager: LightningFastHoldManager) -> Dict[str, Any]:
        """Calculate and return lightning test results"""
        dispatch_times = self.test_results['dispatch_times']
        
        if not dispatch_times:
            return {'error': 'No valid dispatch times recorded'}
        
        # Calculate statistics
        avg_dispatch_time = statistics.mean(dispatch_times)
        median_dispatch_time = statistics.median(dispatch_times)
        min_dispatch_time = min(dispatch_times)
        max_dispatch_time = max(dispatch_times)
        
        # Calculate percentiles
        p95_dispatch_time = statistics.quantiles(dispatch_times, n=20)[18]  # 95th percentile
        p99_dispatch_time = statistics.quantiles(dispatch_times, n=100)[98]  # 99th percentile
        
        # Calculate success rates
        total_tests = self.test_results['total_tests']
        success_rate = (self.test_results['successful_dispatches'] / total_tests) * 100 if total_tests > 0 else 0
        
        # Calculate performance targets
        under_50ms_total = (self.test_results['under_10ms_count'] + 
                           self.test_results['under_25ms_count'] + 
                           self.test_results['under_50ms_count'])
        under_50ms_percentage = under_50ms_total / total_tests * 100
        under_25ms_percentage = (self.test_results['under_10ms_count'] + self.test_results['under_25ms_count']) / total_tests * 100
        under_10ms_percentage = self.test_results['under_10ms_count'] / total_tests * 100
        
        # Get manager performance stats
        manager_stats = hold_manager.get_performance_stats()
        
        results = {
            'test_summary': {
                'total_tests': total_tests,
                'successful_dispatches': self.test_results['successful_dispatches'],
                'failed_dispatches': self.test_results['failed_dispatches'],
                'success_rate': success_rate
            },
            'dispatch_time_stats': {
                'average_ms': avg_dispatch_time,
                'median_ms': median_dispatch_time,
                'min_ms': min_dispatch_time,
                'max_ms': max_dispatch_time,
                'p95_ms': p95_dispatch_time,
                'p99_ms': p99_dispatch_time
            },
            'performance_targets': {
                'under_10ms_count': self.test_results['under_10ms_count'],
                'under_25ms_count': self.test_results['under_25ms_count'],
                'under_50ms_count': self.test_results['under_50ms_count'],
                'over_50ms_count': self.test_results['over_50ms_count'],
                'under_10ms_percentage': under_10ms_percentage,
                'under_25ms_percentage': under_25ms_percentage,
                'under_50ms_percentage': under_50ms_percentage,
                'lightning_target_met': under_50ms_percentage >= 95.0,  # 95% should be under 50ms
                'ultra_lightning_target_met': under_10ms_percentage >= 80.0  # 80% should be under 10ms
            },
            'manager_stats': manager_stats,
            'raw_dispatch_times': dispatch_times
        }
        
        # Log summary
        logger.info("⚡ LIGHTNING PERFORMANCE TEST RESULTS:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Dispatch Success Rate: {success_rate:.1f}%")
        logger.info(f"   Average Dispatch Time: {avg_dispatch_time:.2f}ms")
        logger.info(f"   Median Dispatch Time: {median_dispatch_time:.2f}ms")
        logger.info(f"   95th Percentile: {p95_dispatch_time:.2f}ms")
        logger.info(f"   Under 10ms: {under_10ms_percentage:.1f}% ({self.test_results['under_10ms_count']}/{total_tests})")
        logger.info(f"   Under 25ms: {under_25ms_percentage:.1f}% ({self.test_results['under_10ms_count'] + self.test_results['under_25ms_count']}/{total_tests})")
        logger.info(f"   Under 50ms: {under_50ms_percentage:.1f}% ({under_50ms_total}/{total_tests})")
        
        # Performance assessment
        if results['performance_targets']['ultra_lightning_target_met']:
            logger.info("⚡ ULTRA-LIGHTNING TARGET MET: >80% of dispatches under 10ms!")
        elif results['performance_targets']['lightning_target_met']:
            logger.info("⚡ LIGHTNING TARGET MET: >95% of dispatches under 50ms!")
        else:
            logger.warning(f"⚠️ LIGHTNING TARGET MISSED: Only {under_50ms_percentage:.1f}% under 50ms (target: >95%)")
        
        # Compare to 800ms baseline
        improvement_factor = 800.0 / avg_dispatch_time
        logger.info(f"🚀 SPEED IMPROVEMENT: {improvement_factor:.1f}x faster than 800ms baseline!")
        
        return results


async def run_lightning_performance_test(event_key: str, chart_key: str, channel_keys: List[str], 
                                        team_id: Optional[str] = None, proxy: Optional[str] = None, 
                                        event_id: Optional[str] = None, num_tests: int = 100) -> Dict[str, Any]:
    """Run the lightning performance test"""
    test_system = LightningPerformanceTest(
        event_key=event_key,
        chart_key=chart_key,
        channel_keys=channel_keys,
        team_id=team_id,
        proxy=proxy,
        event_id=event_id
    )
    
    return await test_system.run_lightning_test(num_tests)


if __name__ == "__main__":
    # Example usage
    async def main():
        # These would be provided by the main application
        event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
        chart_key = "example_chart_key"
        channel_keys = ["NO_CHANNEL"]
        team_id = None  # Test without team
        
        results = await run_lightning_performance_test(
            event_key=event_key,
            chart_key=chart_key,
            channel_keys=channel_keys,
            team_id=team_id,
            num_tests=50
        )
        
        print("Lightning test completed!")
        print(f"Average dispatch time: {results['dispatch_time_stats']['average_ms']:.2f}ms")
        print(f"Under 50ms: {results['performance_targets']['under_50ms_percentage']:.1f}%")
        print(f"Under 10ms: {results['performance_targets']['under_10ms_percentage']:.1f}%")
    
    asyncio.run(main())
