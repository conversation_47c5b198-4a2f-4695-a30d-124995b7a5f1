import asyncio
import httpx
import json
import time
import logging
from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple
from chart_token_manager import generate_x_signature as central_generate_x_signature

logger = logging.getLogger("webook_pro")

# --- Configuration ---
SEATSIO_IP = "*************"
SEATSIO_DOMAINS = ["cdn-eu.seatsio.net"]

# --- Channel Key Building ---
def build_channel_keys(channel_keys, team_id):
    """Build proper channel keys for seat operations"""
    if not channel_keys or channel_keys == ['NO_CHANNEL']:
        return ['NO_CHANNEL']

    if not hasattr(build_channel_keys, '_cache'):
        build_channel_keys._cache = {}

    cache_key = (json.dumps(channel_keys, sort_keys=True), team_id)
    if cache_key in build_channel_keys._cache:
        return build_channel_keys._cache[cache_key]

    keys = []
    if isinstance(channel_keys, dict):
        if 'common' in channel_keys and channel_keys['common']:
            keys.extend(['NO_CHANNEL', channel_keys['common'][0]])
            if team_id and team_id in channel_keys and channel_keys[team_id]:
                keys.append(channel_keys[team_id][0])
            else:
                for k, v in channel_keys.items():
                    if k != 'common' and v:
                        keys.append(v[0])
                        break

    if not keys:
        keys.append('NO_CHANNEL')
        if team_id and isinstance(channel_keys, dict) and team_id in channel_keys and channel_keys[team_id]:
            keys.append(channel_keys[team_id][0])

    build_channel_keys._cache[cache_key] = keys
    return keys

# --- Centralized Asynchronous HTTP Client Manager ---
_async_clients: Dict[str, httpx.AsyncClient] = {}
_client_lock = asyncio.Lock()

async def get_http_client(proxy: Optional[str] = None) -> httpx.AsyncClient:
    """
    Retrieves a shared httpx.AsyncClient for a given proxy configuration.
    """
    proxy_key = proxy if proxy else "no_proxy"

    async with _client_lock:
        if proxy_key not in _async_clients:
            logger.info(f"Creating new async client for proxy: {proxy_key}")
            client_kwargs = {
                'http2': True,
                'timeout': httpx.Timeout(10.0),
                'verify': False,
                'limits': httpx.Limits(max_connections=500, max_keepalive_connections=100)
            }
            if proxy:
                proxy_url = f"http://{proxy.split(':')[2]}:{proxy.split(':')[3]}@{proxy.split(':')[0]}:{proxy.split(':')[1]}"
                client_kwargs['proxy'] = proxy_url
            
            _async_clients[proxy_key] = httpx.AsyncClient(**client_kwargs)
        return _async_clients[proxy_key]

def _prepare_seatsio_url(url: str) -> Tuple[str, Dict[str, str]]:
    """Optimizes Seats.io URLs by replacing domain with IP and adding Host header."""
    headers = {}
    for domain in SEATSIO_DOMAINS:
        if domain in url:
            url = url.replace(domain, SEATSIO_IP)
            headers['Host'] = domain
            break
    return url, headers

async def async_make_request(method: str, url: str, proxy: Optional[str] = None, **kwargs) -> httpx.Response:
    """The single, centralized function for all asynchronous HTTP requests."""
    client = await get_http_client(proxy)
    
    url, host_header = _prepare_seatsio_url(url)
    
    if 'headers' not in kwargs:
        kwargs['headers'] = {}
    
    kwargs['headers'].update(host_header)
    
    try:
        response = await client.request(method, url, **kwargs)
        return response
    except httpx.RequestError as e:
        logger.error(f"Request failed: {method} {url} via proxy '{proxy}' | Error: {e}")
        return httpx.Response(status_code=0, request=e.request, text=str(e))

# --- Core Asynchronous Seat Management Functions ---

async def hold_seat(
    seat_number: str,
    event_key: str,
    hold_token: str,
    channel_keys: Any = None,
    team_id: Optional[str] = None,
    proxy: Optional[str] = None
) -> bool:
    """Asynchronously holds a single seat."""
    # Build proper channel keys using team information
    built_channel_keys = build_channel_keys(channel_keys, team_id)

    # Use IP address to bypass Cloudflare
    url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'

    json_data = {
        'events': [event_key], 'holdToken': hold_token,
        'objects': [{'objectId': seat_number}], 'channelKeys': built_channel_keys,
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))

    # Generate random browser ID
    import secrets
    browser_id = secrets.token_hex(8)

    headers = {
        'Host': 'cdn-eu.seatsio.net',
        'accept': '*/*',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'x-client-tool': 'Renderer',
        'x-browser-id': browser_id,
        'x-signature': central_generate_x_signature(body_str)
    }

    response = await async_make_request('POST', url, proxy=proxy, content=body_str, headers=headers)

    if response.status_code == 204:
        logger.info(f"Successfully held seat {seat_number} with token {hold_token[:8]}...")
        return True
    else:
        logger.error(f"Failed to hold seat {seat_number}. Status: {response.status_code}, Msg: {response.text[:100]}")
        return False

async def release_seat(seat_number: str, event_key: str, hold_token: str, proxy: Optional[str] = None) -> bool:
    """Asynchronously releases a single seat."""
    # Use IP address to bypass Cloudflare
    url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects'

    json_data = {
        'events': [event_key], 'holdToken': hold_token,
        'objects': [{'objectId': seat_number}],
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))

    # Generate random browser ID
    import secrets
    browser_id = secrets.token_hex(8)

    headers = {
        'Host': 'cdn-eu.seatsio.net',
        'accept': '*/*',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'x-client-tool': 'Renderer',
        'x-browser-id': browser_id,
        'x-signature': central_generate_x_signature(body_str)
    }
    
    response = await async_make_request('POST', url, proxy=proxy, content=body_str, headers=headers)
    return response.status_code == 204

async def switch_seat_immediate(
    seat_number: str, 
    event_key: str, 
    old_token: str, 
    new_token: str,
    channel_keys: List[str] = None,
    proxy: Optional[str] = None
) -> bool:
    """Performs a high-speed, near-simultaneous release and hold operation."""
    if not channel_keys:
        channel_keys = ['NO_CHANNEL']

    release_url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/release-held-objects'
    release_json = {'events': [event_key], 'holdToken': old_token, 'objects': [{'objectId': seat_number}]}
    release_body = json.dumps(release_json, separators=(',', ':'))
    release_headers = {'Host': 'cdn-eu.seatsio.net', 'content-type': 'application/json', 'x-signature': central_generate_x_signature(release_body)}

    hold_url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    hold_json = {'events': [event_key], 'holdToken': new_token, 'objects': [{'objectId': seat_number}], 'channelKeys': channel_keys}
    hold_body = json.dumps(hold_json, separators=(',', ':'))
    hold_headers = {'Host': 'cdn-eu.seatsio.net', 'content-type': 'application/json', 'x-signature': central_generate_x_signature(hold_body)}

    start_time = time.perf_counter()
    release_task = asyncio.create_task(async_make_request('POST', release_url, proxy=proxy, content=release_body, headers=release_headers))
    hold_task = asyncio.create_task(async_make_request('POST', hold_url, proxy=proxy, content=hold_body, headers=hold_headers))

    release_resp, hold_resp = await asyncio.gather(release_task, hold_task)
    total_time = (time.perf_counter() - start_time) * 1000

    if hold_resp.status_code == 204:
        logger.info(f"✅ SWITCHED seat {seat_number} in {total_time:.2f}ms")
        return True
    else:
        logger.error(f"❌ FAILED to switch seat {seat_number}. Release: {release_resp.status_code}, Hold: {hold_resp.status_code} ({hold_resp.text[:80]})")
        return False

# --- Utility & Processing Functions ---
class VM:
    @staticmethod
    def deobfuscate(e, t):
        i = 63 & VM.hash_code(t)
        n = bytearray(e)
        for j in range(len(n)):
            n[j] = (n[j] - i) & 0xFF
        return n.decode('utf-8')

    @staticmethod
    def hash_code(e):
        t = 0
        for i in range(len(e)):
            t = (29 * t % 10007 + ord(e[i])) % 10007
        return t

def group_tickets_by_type_and_status(data, free_only=False):
    logger.info(f"Grouping {len(data)} seat entries by type and status (free_only={free_only})")
    ticket_groups = defaultdict(lambda: defaultdict(dict))
    for entry in data:
        seat_id = entry.get('objectLabelOrUuid')
        if not seat_id:
            continue
        status = entry.get('status', 'free').lower()
        if free_only and status != 'free':
            continue
        ticket_type = seat_id.split('-')[0].strip() if '-' in seat_id else seat_id
        if not ticket_type: ticket_type = "UNKNOWN"
        ticket_groups[ticket_type][status][seat_id] = entry

    # Log summary
    total_types = len(ticket_groups)
    total_seats = sum(len(statuses) for statuses in ticket_groups.values() for statuses in statuses.values())
    logger.info(f"Grouped into {total_types} ticket types with {total_seats} total seats")
    for ticket_type, statuses in ticket_groups.items():
        type_total = sum(len(seats) for seats in statuses.values())
        logger.info(f"  {ticket_type}: {type_total} seats")

    return ticket_groups

async def get_object_statuses(event_key: str, chart_key: str, proxy: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Asynchronously gets object statuses from seatsio API.

    Args:
        event_key: The seatsio event key
        chart_key: The chart key (kept for API compatibility, not used in current implementation)
        proxy: Optional proxy string

    Returns:
        List of seat objects with their statuses
    """
    try:
        logger.info(f"Getting object statuses for event: {event_key}")
        url = f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/object-statuses'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        params = {
            'event_key': event_key,
        }
        response = await async_make_request('GET', url, proxy=proxy, headers=headers, params=params)
        if response.status_code == 200:
            try:
                # Check if content is empty
                if not hasattr(response, 'content') or len(response.content) == 0:
                    logger.warning("Empty response content from get_object_statuses")
                    return []

                # Try to deobfuscate the response content using the chart_key (like get_match_info does)
                try:
                    deobfuscated_data = VM.deobfuscate(response.content, chart_key)
                    data = json.loads(deobfuscated_data)
                    logger.debug(f"Successfully deobfuscated object statuses using chart_key")
                except Exception as deobfuscate_error:
                    logger.debug(f"Deobfuscation failed: {deobfuscate_error}, trying direct JSON parsing")

                    # Fallback to direct JSON parsing if deobfuscation fails
                    try:
                        # Handle potential compression
                        content_encoding = response.headers.get('content-encoding', '').lower()
                        raw_content = response.content

                        # Handle gzip compression
                        if content_encoding == 'gzip' or raw_content.startswith(b'\x1f\x8b'):
                            try:
                                import gzip
                                raw_content = gzip.decompress(raw_content)
                            except Exception as e:
                                logger.warning(f"Failed to decompress gzip content: {e}")

                        # Handle deflate compression
                        elif content_encoding == 'deflate':
                            try:
                                import zlib
                                raw_content = zlib.decompress(raw_content)
                            except Exception as e:
                                logger.warning(f"Failed to decompress deflate content: {e}")

                        # Try to decode content with proper encoding
                        try:
                            content_str = raw_content.decode('utf-8')
                            if not content_str.strip():
                                logger.warning("Empty or whitespace response from get_object_statuses")
                                return []
                            data = json.loads(content_str)
                        except UnicodeDecodeError:
                            # Fallback to latin-1 if utf-8 fails
                            content_str = raw_content.decode('latin-1')
                            if not content_str.strip():
                                logger.warning("Empty or whitespace response from get_object_statuses (latin-1)")
                                return []
                            data = json.loads(content_str)
                    except Exception as e:
                        logger.error(f"All decoding methods failed: {e}")
                        # Last resort: try using httpx's built-in json() method
                        data = response.json()

                if isinstance(data, dict) and 'objects' in data:
                    objects = data['objects']
                    logger.info(f"Successfully retrieved {len(objects)} seat objects")
                    return objects
                elif isinstance(data, list):
                    logger.info(f"Successfully retrieved {len(data)} seat objects (list format)")
                    return data
                else:
                    logger.error(f"Unexpected response format from get_object_statuses: {type(data)}")
                    return []

            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"Error decoding response from get_object_statuses: {str(e)} - Response content: {response.content[:100] if hasattr(response, 'content') else 'No content'}")
                return []
        else:
            logger.error(f"Failed to get object statuses: {response.status_code} - {response.text[:100]}")
            return []
    except Exception as e:
        logger.error(f"Error getting object statuses: {str(e)}")
        return []

async def get_event_render_data(seatsio_event_key: str, proxy: Optional[str] = None) -> dict:
    """
    Asynchronously gets event render data from seatsio API.

    Args:
        seatsio_event_key: The seatsio event key (different from webook event key)
        proxy: Optional proxy string

    Returns:
        Dict containing render data or error information
    """
    try:
        url = f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/rendering-info'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'cache-control': 'no-cache',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-browser-id': '4efc8a17fc583772',
            'x-client-tool': 'Renderer',
            'x-kl-saas-ajax-request': 'Ajax_Request',
            'x-request-origin': 'webook.com',
            'x-signature': central_generate_x_signature("")
        }
        params = {'event_key': seatsio_event_key}

        response = await async_make_request('GET', url, proxy=proxy, headers=headers, params=params)

        if response.status_code == 200:
            try:
                # Handle potential encoding issues
                if hasattr(response, 'content') and response.content:
                    # Try to decode content with proper encoding
                    try:
                        content_str = response.content.decode('utf-8')
                        data = json.loads(content_str)
                    except UnicodeDecodeError:
                        # Fallback to latin-1 if utf-8 fails
                        content_str = response.content.decode('latin-1')
                        data = json.loads(content_str)
                else:
                    data = response.json()

                logger.info(f"Successfully got render data for seatsio event: {seatsio_event_key}")
                return data
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"Error decoding render data response: {str(e)}")
                return {'Error': f'Decoding error: {str(e)}'}
        else:
            logger.error(f"Failed to get render data: {response.status_code} - {response.text[:100]}")
            return {'Error': f'HTTP {response.status_code}: {response.text[:100]}'}

    except Exception as e:
        logger.error(f"Error getting event render data: {str(e)}")
        return {'Error': str(e)}

async def get_match_info(chart_key: str, drawing_version: int, proxy: Optional[str] = None) -> dict:
    """
    Asynchronously gets match info from seatsio API.

    Args:
        chart_key: The chart key
        drawing_version: The drawing version from render data
        proxy: Optional proxy string

    Returns:
        Dict containing match data or error information
    """
    try:
        url = f'https://cdn-eu.seatsio.net/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/charts/{chart_key}/published/{drawing_version}'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'cache-control': 'no-cache',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-browser-id': '4efc8a17fc583772',
            'x-client-tool': 'Renderer',
            'x-kl-saas-ajax-request': 'Ajax_Request',
            'x-signature': central_generate_x_signature("")
        }

        response = await async_make_request('GET', url, proxy=proxy, headers=headers)

        if response.status_code == 200:
            # Deobfuscate the response content using the chart_key
            deobfuscated_data = VM.deobfuscate(response.content, chart_key)
            match_data = json.loads(deobfuscated_data)
            logger.info(f"Successfully got match info for chart: {chart_key}")
            return match_data
        else:
            logger.error(f"Failed to get match info: {response.status_code} - {response.text[:100]}")
            return {'Error': f'HTTP {response.status_code}: {response.text[:100]}'}

    except Exception as e:
        logger.error(f"Error getting match info: {str(e)}")
        return {'Error': str(e)}

async def get_event_seatsio_info(seatsio_data: dict, proxy: Optional[str] = None) -> dict:
    """
    Asynchronously gets complete seatsio event information.

    Args:
        seatsio_data: Dict containing seatsio data with chart_key and event_key/season_key
        proxy: Optional proxy string

    Returns:
        Dict containing complete event information or error
    """
    try:
        chart_key = seatsio_data['chart_key']
        # Use season_key if available (for seasonal events), otherwise use event_key
        # This is the seatsio event key, different from the webook event key
        seatsio_event_key = seatsio_data.get('season_key') or seatsio_data['event_key']

        logger.info(f"Getting seatsio info - Chart: {chart_key}, Seatsio Event: {seatsio_event_key}")

        # Get render data first
        render_data = await get_event_render_data(seatsio_event_key, proxy=proxy)
        if 'Error' in render_data:
            logger.critical(f"Error getting event render data: {render_data['Error']}")
            return render_data

        # Get match data using the drawing version from render data
        drawing_version = render_data.get('drawingVersion')
        if not drawing_version:
            logger.error("No drawing version found in render data")
            return {'Error': 'No drawing version found in render data'}

        match_data = await get_match_info(chart_key, drawing_version, proxy=proxy)
        if 'Error' in match_data:
            logger.critical(f"Error getting match info: {match_data['Error']}")
            return match_data

        # Add season info to match data
        match_data['season_info'] = render_data.get('seasonStructure')

        logger.info(f"Successfully got complete seatsio info for event: {seatsio_event_key}")
        return match_data

    except Exception as e:
        logger.error(f"Error in get_event_seatsio_info: {str(e)}")
        return {'Error': str(e)}


async def activate_hold_token(hold_token: str, proxy: Optional[str] = None) -> int:
    """
    Activates a hold token and returns the time left in seconds.
    """
    try:
        url = f'https://{SEATSIO_IP}/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/hold-tokens/{hold_token}'
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': 'application/json',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = await async_make_request('POST', url, proxy=proxy, headers=headers, json={})
        if response.status_code == 200:
            try:
                # Handle potential encoding issues
                if hasattr(response, 'content') and response.content:
                    # Try to decode content with proper encoding
                    try:
                        content_str = response.content.decode('utf-8')
                        data = json.loads(content_str)
                    except UnicodeDecodeError:
                        # Fallback to latin-1 if utf-8 fails
                        content_str = response.content.decode('latin-1')
                        data = json.loads(content_str)
                else:
                    data = response.json()

                time_left = data.get('expiresInSeconds', 590)
                logger.info(f"Activated hold token {hold_token[:8]}... with {time_left} seconds remaining")
                return time_left
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                logger.error(f"Error decoding activate token response: {str(e)}")
                return 590
        else:
            logger.error(f"Failed to activate hold token: {response.status_code} - {response.text[:100]}")
            return 590
    except Exception as e:
        logger.error(f"Error activating hold token: {str(e)}")
        return 590