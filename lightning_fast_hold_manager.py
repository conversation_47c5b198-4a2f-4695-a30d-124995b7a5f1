"""
Lightning Fast Hold Manager - Eliminates 800ms delay with extreme optimizations
Uses synchronous requests with persistent connections for sub-50ms performance
"""

import time
import threading
import json
import secrets
import requests
from typing import Optional, List, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
from concurrent.futures import Thread<PERSON>oolExecutor
from collections import deque
import logging

from helper import build_channel_keys
from token_retrieval import get_hold_token, get_cached_event_id
from chart_token_manager import generate_x_signature

logger = logging.getLogger("webook_pro")

# Global persistent session and thread pool for maximum performance
_global_session = None
_global_executor = None
_session_lock = threading.Lock()

def get_global_session(proxy: Optional[str] = None) -> requests.Session:
    """Get or create a global persistent HTTP session"""
    global _global_session
    with _session_lock:
        if _global_session is None:
            _global_session = requests.Session()
            
            # Optimize session for speed
            _global_session.verify = False
            _global_session.timeout = 5.0
            
            # Connection pooling for speed
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=20,
                pool_maxsize=50,
                max_retries=0  # No retries for speed
            )
            _global_session.mount('http://', adapter)
            _global_session.mount('https://', adapter)
            
            # Set proxy if provided
            if proxy:
                parts = proxy.split(':')
                if len(parts) == 4:
                    proxy_url = f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
                    _global_session.proxies = {'http': proxy_url, 'https': proxy_url}
        
        return _global_session

def get_global_executor() -> ThreadPoolExecutor:
    """Get or create a global thread pool executor"""
    global _global_executor
    if _global_executor is None:
        _global_executor = ThreadPoolExecutor(
            max_workers=20,
            thread_name_prefix="LightningHold"
        )
    return _global_executor


class LightningFastHoldManager(QObject):
    """
    Lightning Fast Hold Manager - Eliminates all delays for sub-50ms performance
    Uses synchronous requests with persistent connections
    """
    
    # Signals
    seat_held_signal = pyqtSignal(str, str)  # seat_id, token
    seat_released_signal = pyqtSignal(str)   # seat_id
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: Optional[str] = None, proxy: Optional[str] = None, event_id: Optional[str] = None):
        super().__init__()
        
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
        
        # Current hold token
        self.current_token = None
        self.token_expire_time = None
        
        # Performance tracking
        self.performance_stats = {
            'holds_attempted': 0,
            'holds_successful': 0,
            'average_response_time': 0.0,
            'last_response_times': deque(maxlen=50)
        }
        
        # Lightning-fast optimizations
        self._setup_lightning_optimizations()
        
        # Initialize with a token
        self._initialize_token()
    
    def _setup_lightning_optimizations(self):
        """Setup extreme performance optimizations"""
        # Pre-build channel keys to avoid repeated computation
        self.built_channel_keys = build_channel_keys(self.channel_keys, self.team_id)
        
        # Pre-build base request template
        self.base_request = {
            'events': [self.event_key],
            'holdToken': None,  # Will be filled in
            'objects': [{'objectId': None}],  # Will be filled in
            'channelKeys': self.built_channel_keys,
            'validateEventsLinkedToSameChart': True,
        }
        
        # Pre-build headers template with all required headers
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0',
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'X-Client-Tool': 'Renderer',
            'Origin': 'https://cdn-eu.seatsio.net',
            'Connection': 'keep-alive',
        }
        
        # Pre-generate browser IDs for rotation
        self.browser_ids = [secrets.token_hex(8) for _ in range(20)]
        self.browser_id_index = 0
        
        # Seatsio API endpoint with IP for Cloudflare bypass
        self.hold_url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        
        # Pre-serialize common parts
        self.base_json_template = json.dumps(self.base_request, separators=(',', ':'))
    
    def _initialize_token(self):
        """Get an initial hold token asynchronously"""
        def get_token_async():
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    event_id = self.event_id or get_cached_event_id()
                    token = loop.run_until_complete(
                        get_hold_token(event_id, proxy=self.proxy)
                    )
                    if token:
                        self.current_token = token
                        self.token_expire_time = time.time() + (15 * 60)  # 15 minutes
                        self.log_signal.emit(f"⚡ Lightning token ready: {token[:8]}...")
                    else:
                        self.log_signal.emit("❌ Failed to get lightning token")
                finally:
                    loop.close()
            except Exception as e:
                self.log_signal.emit(f"❌ Error getting lightning token: {str(e)}")
        
        # Run in background thread
        threading.Thread(target=get_token_async, daemon=True).start()
    
    def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid token"""
        if not self.current_token:
            return False
        
        # Check if token is about to expire (within 2 minutes)
        if self.token_expire_time and (time.time() + 120) >= self.token_expire_time:
            self._initialize_token()
            return bool(self.current_token)
        
        return True
    
    def _get_next_browser_id(self) -> str:
        """Get next browser ID from pre-generated pool"""
        browser_id = self.browser_ids[self.browser_id_index]
        self.browser_id_index = (self.browser_id_index + 1) % len(self.browser_ids)
        return browser_id
    
    def hold_seat_lightning_fast(self, seat_id: str) -> bool:
        """
        Lightning-fast seat holding with sub-50ms target
        Returns immediately, actual hold happens synchronously in thread pool
        """
        if not self._ensure_valid_token():
            self.log_signal.emit(f"❌ No valid token for lightning hold: {seat_id}")
            return False
        
        try:
            start_time = time.perf_counter()  # High precision timer
            
            # Pre-build everything for maximum speed
            request_data = self.base_request.copy()
            request_data['holdToken'] = self.current_token
            request_data['objects'] = [{'objectId': seat_id}]
            
            # Pre-build headers
            headers = self.base_headers.copy()
            headers['X-Browser-Id'] = self._get_next_browser_id()
            
            # Serialize request body once
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['X-Signature'] = generate_x_signature(body_str)
            
            # Submit to thread pool for immediate return
            executor = get_global_executor()
            executor.submit(self._execute_lightning_hold, seat_id, body_str, headers, start_time)
            
            return True
            
        except Exception as e:
            self.log_signal.emit(f"❌ Error queuing lightning hold for {seat_id}: {str(e)}")
            return False
    
    def _execute_lightning_hold(self, seat_id: str, body_str: str, headers: Dict[str, str], start_time: float):
        """Execute the lightning-fast hold operation using synchronous requests"""
        try:
            # Use persistent session for maximum speed
            session = get_global_session(self.proxy)
            
            # Make synchronous request - no async overhead
            response = session.post(
                self.hold_url,
                data=body_str,
                headers=headers,
                timeout=5.0
            )
            
            response_time = (time.perf_counter() - start_time) * 1000.0
            success = response.status_code == 204
            
            # Update performance stats
            self._update_performance_stats(success, response_time)
            
            if success:
                self.seat_held_signal.emit(seat_id, self.current_token)
                self.log_signal.emit(f"⚡ Lightning held {seat_id} in {response_time:.1f}ms")
            else:
                self.log_signal.emit(f"❌ Lightning hold failed for {seat_id}: {response.status_code}")
                logger.warning(f"Lightning hold failed for {seat_id}: {response.status_code} - {response.text[:100]}")
                
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000.0
            self._update_performance_stats(False, response_time)
            self.log_signal.emit(f"❌ Lightning hold error for {seat_id}: {str(e)}")
            logger.error(f"Lightning hold error for {seat_id}: {str(e)}")
    
    def _update_performance_stats(self, success: bool, response_time: float):
        """Update performance statistics with lightning monitoring"""
        self.performance_stats['holds_attempted'] += 1
        if success:
            self.performance_stats['holds_successful'] += 1
        
        self.performance_stats['last_response_times'].append(response_time)
        
        # Calculate rolling average
        if self.performance_stats['last_response_times']:
            self.performance_stats['average_response_time'] = sum(
                self.performance_stats['last_response_times']
            ) / len(self.performance_stats['last_response_times'])
        
        # Performance monitoring - log if exceeding targets
        if response_time > 100.0:
            logger.warning(f"⚠️ Lightning hold {response_time:.1f}ms exceeds 100ms target")
        elif response_time > 50.0:
            logger.info(f"🟡 Lightning hold {response_time:.1f}ms (target: <50ms)")
        else:
            logger.debug(f"⚡ Lightning hold {response_time:.1f}ms (excellent)")
        
        # Calculate success rate
        success_rate = (self.performance_stats['holds_successful'] / 
                       self.performance_stats['holds_attempted']) * 100
        
        # Emit performance signal
        self.performance_signal.emit({
            'avg_ms': response_time,
            'success': success,
            'operation': 'lightning_hold',
            'success_rate': success_rate,
            'total_attempts': self.performance_stats['holds_attempted'],
            'rolling_avg_ms': self.performance_stats['average_response_time']
        })
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current lightning performance statistics"""
        if not self.performance_stats['holds_attempted']:
            return {
                'holds_attempted': 0,
                'holds_successful': 0,
                'success_rate': 0.0,
                'average_response_time': 0.0,
                'target_met_percentage': 0.0
            }
        
        success_rate = (self.performance_stats['holds_successful'] / 
                       self.performance_stats['holds_attempted']) * 100
        
        # Calculate percentage of holds meeting <50ms target
        under_50ms = sum(1 for t in self.performance_stats['last_response_times'] if t < 50.0)
        target_met_percentage = (under_50ms / len(self.performance_stats['last_response_times'])) * 100
        
        return {
            'holds_attempted': self.performance_stats['holds_attempted'],
            'holds_successful': self.performance_stats['holds_successful'],
            'success_rate': success_rate,
            'average_response_time': self.performance_stats['average_response_time'],
            'target_met_percentage': target_met_percentage,
            'recent_response_times': list(self.performance_stats['last_response_times'])
        }
    
    def renew_token_if_needed(self):
        """Check and renew token if needed"""
        if not self.current_token or not self.token_expire_time:
            self._initialize_token()
            return
        
        # Renew if less than 5 minutes remaining
        if (time.time() + 300) >= self.token_expire_time:
            self._initialize_token()
    
    def cleanup(self):
        """Cleanup resources"""
        global _global_session, _global_executor
        
        # Close global session
        if _global_session:
            try:
                _global_session.close()
                _global_session = None
            except Exception as e:
                logger.debug(f"Session cleanup error (non-critical): {e}")
        
        # Shutdown executor
        if _global_executor:
            _global_executor.shutdown(wait=False)
            _global_executor = None


# Alias for backward compatibility
UltraFastHoldManager = LightningFastHoldManager
