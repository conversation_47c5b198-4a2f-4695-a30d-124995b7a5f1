# token_management_system.py
import logging
import threading
import time
import queue
import asyncio
from collections import defaultdict
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from token_retrieval import get_hold_token as async_get_hold_token, get_cached_event_id
from helper import activate_hold_token, switch_seat_immediate, hold_seat, release_seat

def get_token_time_remaining(hold_token: str, proxy: str = None) -> int:
    """
    Get time remaining on token (tokens are already active when generated)
    This is just for monitoring, not activation
    """
    try:
        # Tokens are already active when generated, just return default expiry
        # In a real implementation, you could check the actual time remaining
        return 900  # 15 minutes default
    except Exception as e:
        logger.error(f"Error checking token time: {e}")
        return 900

logger = logging.getLogger("webook_pro")

# Constants
TOKEN_RENEWAL_THRESHOLD = 5 * 60  # 5 minutes before expiration
MAX_CONCURRENT_OPERATIONS = 3
MAX_SEATS_PER_TOKEN = 5

class TokenManagerThread(QThread):
    """Dedicated thread for all token operations"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.operation_queue = queue.Queue()
        self.running = True
        
    def run(self):
        """Process operations from the queue"""
        threading.current_thread().name = "TokenManagerThread"
        logger.info("Token manager thread started")
        
        while self.running:
            try:
                # Get next operation with timeout
                operation = self.operation_queue.get(timeout=1.0)
                
                # Execute the operation callback
                callback = operation.get("callback")
                args = operation.get("args", [])
                kwargs = operation.get("kwargs", {})
                
                if callback and callable(callback):
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        logger.error(f"Error executing operation: {str(e)}", exc_info=True)
                        
                # Mark operation as done
                self.operation_queue.task_done()
                
            except queue.Empty:
                # Just a timeout, continue waiting
                continue
                
            except Exception as e:
                logger.error(f"Error in token manager thread: {str(e)}", exc_info=True)
                
        logger.info("Token manager thread stopped")
        
    def stop(self):
        """Stop the thread"""
        self.running = False
        
    def queue_operation(self, callback, *args, **kwargs):
        """Queue an operation to be executed in this thread"""
        self.operation_queue.put({
            "callback": callback,
            "args": args,
            "kwargs": kwargs
        })


class TokenManagementSystem(QObject):
    """
    Unified system for managing tokens, seats, and renewal operations.
    All token operations happen in a single dedicated thread.
    
    Updated to work with the new token retrieval system.
    """
    
    # Signals for UI updates and notifications
    log_signal = pyqtSignal(str)
    seat_held_signal = pyqtSignal(str, str)  # seat_id, token_id
    seat_released_signal = pyqtSignal(str)  # seat_id
    seat_transferred_signal = pyqtSignal(str, str, bool)  # seat_id, token_id, success
    seat_expired_signal = pyqtSignal(list)  # list of expired seat_ids
    token_renewed_signal = pyqtSignal(str, int)  # token_id, new_time_left
    token_expired_signal = pyqtSignal(str)  # token_id
    ui_update_signal = pyqtSignal()  # General signal to update UI
    
    def __init__(self, event_key, chart_key, channel_keys, team_id=None, proxies=None):
        super().__init__()
        
        # Core properties
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.team_id = team_id
        self.proxies = proxies if proxies else []
        
        # Token and seat tracking
        self.tokens = {}  # token_id -> {expire_time, time_left, is_renewing}
        self.token_to_seats = defaultdict(set)  # token_id -> set of seat_ids
        self.seat_to_token = {}  # seat_id -> token_id
        
        # Thread safety
        self.token_lock = threading.RLock()  # Reentrant lock for token operations
        self.seat_lock = threading.RLock()  # Reentrant lock for seat operations
        
        # Create and start the token manager thread
        self.token_thread = TokenManagerThread(self)
        self.token_thread.setObjectName(f"TokenMgr-{event_key[:6]}")
        self.token_thread.start()
        
        # Cleanup and renewal timers - these should run on the main thread
        # but queue actual work to the token thread
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self._queue_cleanup_expired_tokens)
        self.cleanup_timer.start(5 * 1000)  # Every 5 seconds (more frequent than before)
        
        self.renewal_timer = QTimer()
        self.renewal_timer.timeout.connect(self._queue_check_token_renewals)
        self.renewal_timer.start(30 * 1000)  # Every 30 seconds
        
        # Status tracking
        self.running = True
        self.operation_counts = {
            "hold": 0,
            "release": 0,
            "transfer": 0,
            "renew": 0
        }
        
        # Fast hold tracking - for compatibility with FastHoldManager
        self._transfer_in_progress_seats = set()
        
        # Get the event ID from the event key and store it
        # This is important for token creation
        self._cache_event_id_from_key()
        
        self.log_signal.emit("Token Management System initialized")
    
    def _cache_event_id_from_key(self):
        """Attempt to cache the event ID based on the event key"""
        # This method is no longer needed since event ID is cached
        # during the main event loading process in main_window.py
        # The cache_event_id function is called there with the full event data
        pass
        
    #------------------------------------------------------------------
    # Fast Hold Manager Integration
    #------------------------------------------------------------------
    
    def register_fast_hold_manager(self, fast_hold_manager):
        """
        Register a FastHoldManager with this token management system.
        This allows the TokenManagementSystem to handle all seat tracking.
        """
        if not fast_hold_manager:
            return False
            
        # Connect signals from FastHoldManager to our handlers
        fast_hold_manager.seat_held_signal.connect(self.on_fast_hold_seat_held)
        
        # Store reference to FastHoldManager
        self.fast_hold_manager = fast_hold_manager
        
        # Let FastHoldManager use our token for future holds
        if fast_hold_manager.token:
            self.register_token(fast_hold_manager.token, fast_hold_manager.expire_time)
            
        self.log_signal.emit("FastHoldManager registered with TokenManagementSystem")
        return True
        
    def on_fast_hold_seat_held(self, seat_id, token_id):
        """
        Handle seats held by FastHoldManager.
        This ensures all seats are tracked in one place regardless of how they're held.
        """
        # Register the seat with our system
        self.register_held_seat(seat_id, token_id)
    
    #------------------------------------------------------------------
    # Token Creation and Management
    #------------------------------------------------------------------
    
    def create_token(self):
        """
        Queue creation of a new token in the token thread.
        Returns None immediately; the actual token will be created asynchronously.
        """
        self.token_thread.queue_operation(self._execute_create_token)
        return None  # Token will be created asynchronously
    
    def create_token_sync(self):
        """
        Create a new token synchronously (for initial setup).
        This method should only be called during initialization.
        """
        try:
            # Get a random proxy
            proxy = self._get_random_proxy()
            
            # Get event ID from cache
            event_id = get_cached_event_id()
            if not event_id:
                self.log_signal.emit("No event ID available for token creation, please load an event first")
                return None
            
            # Get new hold token using the async method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                token = loop.run_until_complete(async_get_hold_token(event_id=event_id, proxy=proxy))
            finally:
                pass
            if not token:
                self.log_signal.emit("Failed to get new token")
                return None
            
            # Tokens are already active when generated, just set expiry
            time_left = get_token_time_remaining(token, proxy)
            expire_time = time.time() + time_left
            
            # Store token information
            with self.token_lock:
                self.tokens[token] = {
                    "expire_time": expire_time,
                    "time_left": time_left,
                    "is_renewing": False
                }
                
            self.log_signal.emit(f"Created new token: {token}")
            return token
            
        except Exception as e:
            self.log_signal.emit(f"Error creating token: {str(e)}")
            logger.error(f"Error creating token: {str(e)}", exc_info=True)
            return None
    
    def _execute_create_token(self):
        """
        Create a new token in the token thread.
        """
        try:
            # Get a random proxy
            proxy = self._get_random_proxy()
            
            # Get event ID from cache
            event_id = get_cached_event_id()
            if not event_id:
                self.log_signal.emit("No event ID available for token creation, please load an event first")
                return None
            
            # Get new hold token using the async method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                token = loop.run_until_complete(async_get_hold_token(event_id=event_id, proxy=proxy))
            finally:
                pass
            if not token:
                self.log_signal.emit("Failed to get new token")
                return None
            
            # Tokens are already active when generated, just set expiry
            time_left = get_token_time_remaining(token, proxy)
            expire_time = time.time() + time_left
            
            # Store token information
            with self.token_lock:
                self.tokens[token] = {
                    "expire_time": expire_time,
                    "time_left": time_left,
                    "is_renewing": False
                }
                
            self.log_signal.emit(f"Created new token: {token}")
            return token
            
        except Exception as e:
            self.log_signal.emit(f"Error creating token: {str(e)}")
            logger.error(f"Error creating token: {str(e)}", exc_info=True)
            return None
    
    def register_token(self, token_id, expire_time=None):
        """
        Register an existing token with the management system.
        This is useful for integrating with other components that create tokens.
        """
        if not token_id:
            return False
            
        with self.token_lock:
            # Check if token already exists
            if token_id in self.tokens:
                return True
                
            # If expire_time is provided, use it, otherwise activate to get time
            if expire_time:
                time_left = max(0, int(expire_time - time.time()))
            else:
                # Tokens are already active, just get time remaining
                proxy = self._get_random_proxy()
                time_left = get_token_time_remaining(token_id, proxy)
                expire_time = time.time() + time_left
                
            # Register the token
            self.tokens[token_id] = {
                "expire_time": expire_time,
                "time_left": time_left,
                "is_renewing": False
            }
            
            self.log_signal.emit(f"Registered existing token: {token_id}")
            return True
    
    def get_token_status(self, token_id):
        """
        Get status information for a token.
        Returns a dict with token info or None if not found.
        This is safe to call from any thread as it only reads data.
        """
        with self.token_lock:
            if token_id not in self.tokens:
                return None
                
            token_info = self.tokens[token_id].copy()
            
            # Calculate current time left
            current_time = time.time()
            token_info["time_left"] = max(0, int(token_info["expire_time"] - current_time))
            
            # Add seat information
            with self.seat_lock:
                token_info["seats"] = list(self.token_to_seats.get(token_id, set()))
                token_info["seat_count"] = len(token_info["seats"])
            
            token_info["token"] = token_id
            
            return token_info


    
    def _queue_check_token_renewals(self):
        """Queue a token renewal check in the token thread"""
        self.token_thread.queue_operation(self._execute_check_token_renewals)
            
    def _execute_check_token_renewals(self):
        """
        Check for tokens that need renewal and execute renewals.
        This runs in the token thread.
        """
        try:
            tokens_to_renew = []
            
            with self.token_lock:
                current_time = time.time()
                
                for token_id, token_info in list(self.tokens.items()):
                    # If token is already expired, remove it immediately
                    if token_info["expire_time"] <= current_time:
                        # Handle expired token
                        self._handle_expired_token(token_id)
                        continue
                        
                    # Skip tokens already being renewed
                    if token_info["is_renewing"]:
                        continue
                        
                    # Skip tokens without seats
                    if not self.token_to_seats[token_id]:
                        continue
                        
                    # Check if renewal is needed
                    time_left = token_info["expire_time"] - current_time
                    if time_left <= TOKEN_RENEWAL_THRESHOLD:
                        tokens_to_renew.append(token_id)
                        token_info["is_renewing"] = True
                                        
            # Process renewal operations right here in the token thread
            for token_id in tokens_to_renew:
                self._execute_token_renewal(token_id)
                
        except Exception as e:
            self.log_signal.emit(f"Error checking token renewals: {str(e)}")
            logger.error(f"Error checking token renewals: {str(e)}", exc_info=True)
    
    def renew_token(self, token_id):
        """
        Queue a token renewal operation in the token thread.
        Returns True if renewal was queued, False otherwise.
        """
        with self.token_lock:
            if token_id not in self.tokens:
                return False
                
            if self.tokens[token_id]["is_renewing"]:
                return False
                
            self.tokens[token_id]["is_renewing"] = True
            
        self.token_thread.queue_operation(self._execute_token_renewal, token_id)
        
        self.log_signal.emit(f"Queued renewal for token {token_id}")
        return True
    
    def _handle_expired_token(self, token_id):
        """
        Handle an expired token by releasing all its seats and removing it.
        This runs in the token thread.
        """
        try:
            # Get seats held by this token
            with self.seat_lock:
                expired_seats = list(self.token_to_seats[token_id])
                
            # Remove the token
            with self.token_lock:
                self.tokens.pop(token_id, None)
                
            # Remove seat associations
            with self.seat_lock:
                self.token_to_seats.pop(token_id, None)
                for seat_id in expired_seats:
                    self.seat_to_token.pop(seat_id, None)
                    
            # Log and notify
            self.log_signal.emit(f"Removed expired token {token_id} with {len(expired_seats)} seats")
            self.token_expired_signal.emit(token_id)
            
            if expired_seats:
                self.seat_expired_signal.emit(expired_seats)
                self.log_signal.emit(f"Removed {len(expired_seats)} expired seats")
                
            # Trigger UI update
            self.ui_update_signal.emit()
            
        except Exception as e:
            self.log_signal.emit(f"Error handling expired token {token_id}: {str(e)}")
            logger.error(f"Error handling expired token {token_id}: {str(e)}", exc_info=True)
    
    def _execute_token_renewal(self, token_id):
        """
        Execute token renewal operation in the token thread.
        Uses concurrent.futures for parallel seat transfers and adds delays to prevent rate limiting.
        """
        try:
            import concurrent.futures
            
            # First check if token still exists
            with self.token_lock:
                if token_id not in self.tokens:
                    logger.debug(f"Token {token_id} no longer exists, skipping renewal")
                    return
                    
            self.log_signal.emit(f"Starting token renewal for {token_id}")
            
            # Get token seats
            with self.seat_lock:
                seats_to_transfer = list(self.token_to_seats[token_id])
                    
            if not seats_to_transfer:
                self.log_signal.emit(f"No seats to transfer for token {token_id}")
                with self.token_lock:
                    if token_id in self.tokens:
                        self.tokens[token_id]["is_renewing"] = False
                return
                
            # Create new token
            new_token = self._execute_create_token()
            if not new_token:
                self.log_signal.emit(f"Failed to create new token for renewal")
                with self.token_lock:
                    if token_id in self.tokens:
                        self.tokens[token_id]["is_renewing"] = False
                return
            
            total_seats = len(seats_to_transfer)
            self.log_signal.emit(f"Attempting to transfer {total_seats} seats from {token_id} to {new_token}")
            
            # Transfer seats using concurrent.futures for parallelism
            success_count = 0
            batch_count = 0
            
            # Process in batches of 5 to maintain concurrency while not overwhelming the API
            batch_size = 5
            
            # Use named thread pool for easier debugging
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=batch_size,
                thread_name_prefix=f"TokenRenew-{token_id[:4]}"
            ) as executor:
                # Process all seats in batches
                for i in range(0, len(seats_to_transfer), batch_size):
                    batch = seats_to_transfer[i:i+batch_size]
                    batch_count += 1
                    
                    # Add a small delay every 5 batches to prevent rate limiting
                    if batch_count % 5 == 0:
                        self.log_signal.emit(f"Adding delay after 5 batches to prevent rate limiting...")
                        time.sleep(1.5)  # 1.5 second delay
                    
                    # Submit all seats in this batch to thread pool
                    seat_futures = {
                        executor.submit(
                            switch_seat_immediate,
                            seat_number=seat_id,
                            event_key=self.event_key,
                            old_token=token_id,
                            new_token=new_token,
                            channel_keys=self.channel_keys,
                            team_id=self.team_id,
                            proxy=self._get_random_proxy(),
                            max_attempts=2
                        ): seat_id for seat_id in batch
                    }
                    
                    # Process results as they complete
                    for future in concurrent.futures.as_completed(seat_futures):
                        seat_id = seat_futures[future]
                        
                        # Check if token is still valid before processing result
                        with self.token_lock:
                            if token_id not in self.tokens:
                                self.log_signal.emit(f"Token {token_id} was removed during seat transfer")
                                break
                                
                            if self.tokens[token_id]["expire_time"] <= time.time():
                                self.log_signal.emit(f"Token {token_id} expired during seat transfer")
                                # Handle as expired
                                self._handle_expired_token(token_id)
                                break
                        
                        try:
                            success = future.result()
                            
                            # Remove seat from transfer tracking
                            with self.seat_lock:
                                self._transfer_in_progress_seats.discard(seat_id)
                            
                            if success:
                                # Update tracking
                                with self.seat_lock:
                                    self.token_to_seats[token_id].discard(seat_id)
                                    self.token_to_seats[new_token].add(seat_id)
                                    self.seat_to_token[seat_id] = new_token
                                    
                                success_count += 1
                                if success_count % 5 == 0:  # Log every 5 successful transfers
                                    self.log_signal.emit(f"Progress: Transferred {success_count}/{total_seats} seats")
                            else:
                                self.log_signal.emit(f"Failed to transfer seat {seat_id} to token {new_token}")
                                
                        except Exception as e:
                            # Remove seat from transfer tracking on error
                            with self.seat_lock:
                                self._transfer_in_progress_seats.discard(seat_id)
                                
                            self.log_signal.emit(f"Error transferring seat {seat_id}: {str(e)}")
                    
                    # Small delay between batches for API friendliness
                    time.sleep(0.2)
            
            # Clean up any remaining seats in transfer_in_progress
            with self.seat_lock:
                for seat_id in seats_to_transfer:
                    self._transfer_in_progress_seats.discard(seat_id)
                    
            # Update renewal status
            with self.token_lock:
                if token_id in self.tokens:
                    self.tokens[token_id]["is_renewing"] = False
                    
            self.log_signal.emit(f"Renewed token {token_id} -> {new_token}: {success_count}/{len(seats_to_transfer)} seats transferred")
            
            # Only emit token renewed signal if we actually transferred seats
            if success_count > 0:
                new_status = self.get_token_status(new_token)
                if new_status:
                    self.token_renewed_signal.emit(new_token, new_status["time_left"])
            
            # Trigger UI update
            self.ui_update_signal.emit()
            
        except Exception as e:
            self.log_signal.emit(f"Error renewing token {token_id}: {str(e)}")
            logger.error(f"Error renewing token {token_id}: {str(e)}", exc_info=True)
            
            # Reset renewal status
            with self.token_lock:
                if token_id in self.tokens:
                    self.tokens[token_id]["is_renewing"] = False
                    
            # Clean up transfer tracking on error
            with self.seat_lock:
                for seat_id in seats_to_transfer:
                    self._transfer_in_progress_seats.discard(seat_id)
    
    def _queue_cleanup_expired_tokens(self):
        """Queue a cleanup operation in the token thread"""
        self.token_thread.queue_operation(self._execute_cleanup_expired_tokens)
    
    @property
    def transfer_in_progress_seats(self):
        """Get a list of seats currently being transferred"""
        with self.seat_lock:
            return set(self._transfer_in_progress_seats)

    def _execute_cleanup_expired_tokens(self):
        """
        Remove expired tokens and notify about expired seats.
        This runs in the token thread.
        """
        try:
            expired_tokens = []
            expired_seats = []
            
            with self.token_lock:
                current_time = time.time()
                
                for token_id, token_info in list(self.tokens.items()):
                    # Check if token is expired
                    if token_info["expire_time"] <= current_time and not token_info["is_renewing"]:
                        expired_tokens.append(token_id)
                        
                        # Collect seats from this token
                        with self.seat_lock:
                            expired_seats.extend(list(self.token_to_seats[token_id]))
                            
            # Handle each expired token
            for token_id in expired_tokens:
                self._handle_expired_token(token_id)
                
        except Exception as e:
            self.log_signal.emit(f"Error cleaning up expired tokens: {str(e)}")
            logger.error(f"Error cleaning up expired tokens: {str(e)}", exc_info=True)
    
    #------------------------------------------------------------------
    # Seat Operations
    #------------------------------------------------------------------
    
    def hold_seat(self, seat_id, token=None):
        """
        Queue a seat hold operation in the token thread.
        If token is None, a new token will be created.
        Returns True if the operation was queued, False otherwise.
        """
        # Queue the operation
        self.token_thread.queue_operation(self._execute_hold_seat, seat_id, token)
        return True
    
    def _execute_hold_seat(self, seat_id, token=None):
        """
        Hold a seat using the specified token or create a new token if none provided.
        This runs in the token thread.
        """
        try:
            # If seat is already held, don't try to hold again
            with self.seat_lock:
                if seat_id in self.seat_to_token:
                    self.log_signal.emit(f"Seat {seat_id} is already held")
                    return False, None
            
            # If no token provided, create one
            if not token:
                token = self._execute_create_token()
                if not token:
                    self.log_signal.emit(f"Failed to create token for seat {seat_id}")
                    return False, None
            
            # Use random proxy
            proxy = self._get_random_proxy()
            
            # Hold the seat
            success = hold_seat(
                seat_number=seat_id,
                event=self.event_key,
                hold_token=token,
                channel_keys=self.channel_keys,
                team_id=self.team_id,
                proxy=proxy
            )
            
            if success:
                # Update tracking
                with self.seat_lock:
                    self.token_to_seats[token].add(seat_id)
                    self.seat_to_token[seat_id] = token
                
                # Make sure token is in our tracking
                with self.token_lock:
                    if token not in self.tokens:
                        # Get current info (tokens are already active)
                        time_left = get_token_time_remaining(token, proxy)
                        expire_time = time.time() + time_left
                        
                        self.tokens[token] = {
                            "expire_time": expire_time,
                            "time_left": time_left,
                            "is_renewing": False
                        }
                
                # Emit signal
                self.seat_held_signal.emit(seat_id, token)
                
                # Update operation count
                self.operation_counts["hold"] += 1
                
                return True
            else:
                return False
                
        except Exception as e:
            self.log_signal.emit(f"Error holding seat {seat_id}: {str(e)}")
            logger.error(f"Error holding seat {seat_id}: {str(e)}", exc_info=True)
            return False
    
    def release_seat(self, seat_id):
        """
        Queue a seat release operation in the token thread.
        Returns True if operation was queued, False otherwise.
        """
        # Queue the operation
        self.token_thread.queue_operation(self._execute_release_seat, seat_id)
        return True
    
    def _execute_release_seat(self, seat_id):
        """
        Release a seat back to the public.
        This runs in the token thread.
        """
        try:
            # Get token for this seat
            with self.seat_lock:
                token = self.seat_to_token.get(seat_id)
                if not token:
                    self.log_signal.emit(f"Seat {seat_id} not found in tracking")
                    return False
            
            # Use random proxy
            proxy = self._get_random_proxy()
            
            # Release the seat
            success = release_seat(seat_id, self.event_key, token)
            
            if success:
                # Update tracking
                with self.seat_lock:
                    self.token_to_seats[token].discard(seat_id)
                    self.seat_to_token.pop(seat_id, None)
                
                # Emit signal
                self.seat_released_signal.emit(seat_id)
                
                # Update operation count
                self.operation_counts["release"] += 1
                
                return True
            else:
                return False
                
        except Exception as e:
            self.log_signal.emit(f"Error releasing seat {seat_id}: {str(e)}")
            logger.error(f"Error releasing seat {seat_id}: {str(e)}", exc_info=True)
            return False
    
    def transfer_seat(self, seat_id, new_token, is_external_token=True):
        """
        Queue an operation to transfer a seat to a different token.
        
        Parameters:
            seat_id: The seat to transfer
            new_token: The token to transfer the seat to
            is_external_token: If True, the new token is external and seats should be fully removed from tracking
            
        Returns:
            True if queued, False otherwise.
        """
        try:
            # Check if seat exists in our tracking
            with self.seat_lock:
                old_token = self.seat_to_token.get(seat_id)
                if not old_token:
                    self.log_signal.emit(f"Seat {seat_id} not found in tracking")
                    return False
                    
                # Track seats being transferred to prevent auto-holding during transfer
                self._transfer_in_progress_seats.add(seat_id)
            
            # Queue the transfer operation
            self.token_thread.queue_operation(
                self._execute_seat_transfer, 
                seat_id, 
                old_token, 
                new_token,
                is_external_token
            )
            
            if is_external_token:
                self.log_signal.emit(f"Queued transfer of seat {seat_id} to external token {new_token}")
            else:
                self.log_signal.emit(f"Queued transfer of seat {seat_id} to internal token {new_token}")
            return True
                
        except Exception as e:
            self.log_signal.emit(f"Error queuing transfer of seat {seat_id}: {str(e)}")
            logger.error(f"Error queuing transfer of seat {seat_id}: {str(e)}", exc_info=True)
            
            # Cleanup on error
            with self.seat_lock:
                self._transfer_in_progress_seats.discard(seat_id)
                
            return False
    
    def _execute_seat_transfer(self, seat_id, old_token, new_token, is_external_token=True):
        """
        Execute seat transfer operation.
        This runs in the token thread.
        
        Parameters:
            seat_id: The seat to transfer
            old_token: The current token holding the seat
            new_token: The token to transfer the seat to
            is_external_token: If True, the new token is external and seats should be fully removed from tracking
        """
        try:
            # First check if new_token is valid - without holding locks
            # Do this before any other operations to avoid deadlocks
            try:
                # Get a random proxy - do this outside any locks
                proxy = self._get_random_proxy()
                
                # Only register non-external tokens in our system
                if not is_external_token:
                    # Check if token exists in our system
                    token_exists = False
                    with self.token_lock:
                        token_exists = new_token in self.tokens
                        
                    # If token doesn't exist, activate it to verify it's valid
                    if not token_exists:
                        # Check token time remaining (tokens are already active)
                        time_left = get_token_time_remaining(new_token, proxy)
                        if not time_left or time_left <= 0:
                            self.log_signal.emit(f"Destination token {new_token} appears to be invalid")
                            # Clean up transfer tracking
                            with self.seat_lock:
                                self._transfer_in_progress_seats.discard(seat_id)
                            
                            # Emit failure signal
                            self.seat_transferred_signal.emit(seat_id, new_token, False)
                            return False
                        
                        # Now register the token - with proper locking
                        with self.token_lock:
                            expire_time = time.time() + time_left
                            self.tokens[new_token] = {
                                "expire_time": expire_time,
                                "time_left": time_left,
                                "is_renewing": False
                            }
                        
                        self.log_signal.emit(f"Registered destination token {new_token}")
            except Exception as e:
                self.log_signal.emit(f"Error validating destination token {new_token}: {str(e)}")
                # Continue anyway - the switch_seat_immediate call will fail if token is invalid
            
            # Check if seat is still in our tracking
            with self.seat_lock:
                current_token = self.seat_to_token.get(seat_id)
                if not current_token:
                    self.log_signal.emit(f"Seat {seat_id} no longer in tracking")
                    # Make sure to remove from in-progress set
                    self._transfer_in_progress_seats.discard(seat_id)
                    self.seat_transferred_signal.emit(seat_id, new_token, False)
                    return False
                
                if current_token != old_token:
                    self.log_signal.emit(f"Seat {seat_id} is now held by token {current_token}, not {old_token}")
                    # Make sure to remove from in-progress set
                    self._transfer_in_progress_seats.discard(seat_id)
                    self.seat_transferred_signal.emit(seat_id, new_token, False)
                    return False
            
            # Execute the switch - this is a network operation, do it WITHOUT holding locks
            success = switch_seat_immediate(
                seat_number=seat_id,
                event_key=self.event_key,
                old_token=old_token,
                new_token=new_token,
                channel_keys=self.channel_keys,
                team_id=self.team_id,
                proxy=proxy
            )
            
            if success:
                # Update tracking - only AFTER the network operation succeeds
                with self.seat_lock:
                    # Remove from old token
                    self.token_to_seats[old_token].discard(seat_id)
                    
                    # For external tokens, just remove the seat completely from tracking
                    # For internal tokens, add to the new token's tracking
                    if is_external_token:
                        # Remove seat from tracking completely
                        self.seat_to_token.pop(seat_id, None)
                    else:
                        # Add to new token's tracking
                        self.token_to_seats[new_token].add(seat_id)
                        self.seat_to_token[seat_id] = new_token
                    
                    # Remove from in-progress set
                    self._transfer_in_progress_seats.discard(seat_id)
                
                # Update operation count
                self.operation_counts["transfer"] += 1
                
                # Emit signal
                self.seat_transferred_signal.emit(seat_id, new_token, True)
                
                # Log success with more details
                if is_external_token:
                    self.log_signal.emit(f"Successfully transferred seat {seat_id} to external token {new_token[:8]}")
                else:
                    self.log_signal.emit(f"Successfully transferred seat {seat_id} from {old_token[:8]} to {new_token[:8]}")
                
                return True
            else:
                # Remove from in-progress set on failure too
                with self.seat_lock:
                    self._transfer_in_progress_seats.discard(seat_id)
                        
                # Emit signal
                self.seat_transferred_signal.emit(seat_id, new_token, False)
                return False
                
        except Exception as e:
            self.log_signal.emit(f"Error transferring seat {seat_id}: {str(e)}")
            logger.error(f"Error transferring seat {seat_id}: {str(e)}", exc_info=True)
            
            # Always clean up on exception
            with self.seat_lock:
                self._transfer_in_progress_seats.discard(seat_id)
            
            # Emit signal
            self.seat_transferred_signal.emit(seat_id, new_token, False)
            return False
    
    #------------------------------------------------------------------
    # Utility Methods
    #------------------------------------------------------------------
    
    def get_all_tokens(self):
        """
        Get list of all token IDs.
        This is safe to call from any thread as it only reads data.
        """
        with self.token_lock:
            return list(self.tokens.keys())
    
    def get_all_seats(self):
        """
        Get dict mapping of all seats to their tokens.
        This is safe to call from any thread as it only reads data.
        """
        with self.seat_lock:
            return self.seat_to_token.copy()
    
    def get_seats_for_token(self, token_id):
        """
        Get list of seats held by a specific token.
        This is safe to call from any thread as it only reads data.
        """
        with self.seat_lock:
            return list(self.token_to_seats.get(token_id, set()))
    
    def _get_random_proxy(self):
        """Get a random proxy from the list"""
        if not self.proxies:
            return None
        import random
        return random.choice(self.proxies)
    
    def register_held_seat(self, seat_id, token):
        """
        Register an already-held seat with the token management system.
        This is used for seats held by FastHoldManager that are already held externally.
        This method is designed to be called from the main thread.
        """
        # Queue the operation
        self.token_thread.queue_operation(self._execute_register_held_seat, seat_id, token)
        return True
    
    def _execute_register_held_seat(self, seat_id, token):
        """Register an already-held seat with proper cleanup."""
        try:
            # Check if seat is already registered with a different token
            old_token = None
            with self.seat_lock:
                if seat_id in self.seat_to_token:
                    old_token = self.seat_to_token[seat_id]
                    
                    # If already registered with this token, just return success
                    if old_token == token:
                        return True
                    
                    # Remove seat from old token's tracking
                    self.log_signal.emit(f"⚠️ Seat {seat_id} was previously registered with token {old_token}, updating...")
                    self.token_to_seats[old_token].discard(seat_id)
                    
                    # If old token has no more seats, remove it
                    if not self.token_to_seats[old_token]:
                        with self.token_lock:
                            self.tokens.pop(old_token, None)
                        self.log_signal.emit(f"Removed empty token {old_token} after seat migration")
            
            # Now register the seat with the new token
            with self.seat_lock:
                self.token_to_seats[token].add(seat_id)
                self.seat_to_token[seat_id] = token
            
            # Make sure token is in our tracking
            with self.token_lock:
                if token not in self.tokens:
                    # Get current info from activation
                    proxy = self._get_random_proxy()

                    # Run the async activation in a separate thread
                    import asyncio
                    import threading
                    import queue

                    result_queue = queue.Queue()

                    def run_activation():
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                result = loop.run_until_complete(activate_hold_token(token, proxy))
                                result_queue.put(('success', result))
                            finally:
                                loop.close()
                        except Exception as e:
                            result_queue.put(('error', str(e)))

                    thread = threading.Thread(target=run_activation)
                    thread.start()
                    thread.join(timeout=5)  # 5 second timeout

                    try:
                        result_type, result_value = result_queue.get_nowait()
                        if result_type == 'success':
                            time_left = result_value
                        else:
                            time_left = 900  # Default 15 minutes
                    except queue.Empty:
                        time_left = 900  # Default 15 minutes

                    expire_time = time.time() + time_left
                    
                    self.tokens[token] = {
                        "expire_time": expire_time,
                        "time_left": time_left,
                        "is_renewing": False
                    }
            
            # Emit signal (only if this is a new registration, not an update)
            if not old_token:
                self.seat_held_signal.emit(seat_id, token)
            
            # Trigger UI update
            self.ui_update_signal.emit()
            
            return True
            
        except Exception as e:
            self.log_signal.emit(f"Error registering seat {seat_id}: {str(e)}")
            logger.error(f"Error registering seat {seat_id}: {str(e)}", exc_info=True)
            return False

    def get_stats(self):
        """
        Get system statistics.
        This is safe to call from any thread as it only reads data.
        """
        with self.token_lock, self.seat_lock:
            return {
                "tokens": len(self.tokens),
                "seats": len(self.seat_to_token),
                "operations": self.operation_counts.copy(),
                "queue_size": self.token_thread.operation_queue.qsize() if hasattr(self.token_thread, 'operation_queue') else 0
            }
    
    def shutdown(self):
        """Clean up and stop all background operations"""
        self.running = False
        
        # Stop timers
        if self.cleanup_timer.isActive():
            self.cleanup_timer.stop()
            
        if self.renewal_timer.isActive():
            self.renewal_timer.stop()
            
        # Stop token thread
        if hasattr(self, 'token_thread') and self.token_thread.isRunning():
            self.token_thread.stop()
            self.token_thread.quit()
            self.token_thread.wait(1000)  # Wait for 1 second max
            
        self.log_signal.emit("Token Management System shut down")
