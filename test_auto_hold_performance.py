#!/usr/bin/env python3
"""
Auto Hold Performance Test
Comprehensive testing of the new auto_hold.py system to validate <100ms performance
"""

import asyncio
import logging
import time
import random
from typing import List

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from auto_hold import initialize_auto_hold, get_performance_stats, log_performance_summary
from token_retrieval import cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

class AutoHoldPerformanceTest:
    """Comprehensive performance testing for auto-hold system"""
    
    def __init__(self, event_url: str, proxy: str = None):
        self.event_url = event_url
        self.proxy = proxy
        self.auto_hold_system = None
        
        # Test results
        self.test_results = []
        self.held_seats = []
        self.failed_seats = []
        
    async def initialize(self) -> bool:
        """Initialize the test system"""
        try:
            logger.info("🚀 Initializing Auto-Hold Performance Test")
            
            # Get event data
            webook_client = WebookClient(proxy=self.proxy)
            try:
                event_data = webook_client.get_event_data(self.event_url)
            finally:
                webook_client.close()
            
            if not event_data:
                logger.error("❌ Failed to get event data")
                return False
            
            # Cache the event ID for token generation
            cache_event_id(event_data)
            
            # Extract seatsio information
            seats_io_data = event_data["data"]["seats_io"]
            self.seatsio_event_key = seats_io_data["event_key"]
            self.chart_key = seats_io_data["chart_key"]
            
            logger.info(f"🎯 Event Key: {self.seatsio_event_key}")
            logger.info(f"🗺️ Chart Key: {self.chart_key}")
            
            # Initialize auto-hold system
            self.auto_hold_system = initialize_auto_hold(
                event_key=self.seatsio_event_key,
                channel_keys=['NO_CHANNEL'],
                team_id=None,
                proxy=self.proxy
            )
            
            # Set up callbacks
            self.auto_hold_system.set_callbacks(
                success_callback=self._on_seat_held,
                failure_callback=self._on_seat_failed
            )
            
            logger.info("✅ Auto-hold system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize: {str(e)}")
            return False
    
    def _on_seat_held(self, seat_id: str, token: str):
        """Callback for successful seat holds"""
        self.held_seats.append(seat_id)
        logger.info(f"✅ Held seat: {seat_id}")
    
    def _on_seat_failed(self, seat_id: str, error: str):
        """Callback for failed seat holds"""
        self.failed_seats.append((seat_id, error))
        logger.error(f"❌ Failed to hold seat {seat_id}: {error}")
    
    async def test_single_seat_performance(self, iterations: int = 10) -> dict:
        """Test single seat hold performance"""
        logger.info(f"🎯 Testing single seat performance ({iterations} iterations)")
        
        response_times = []
        successes = 0
        
        for i in range(iterations):
            seat_id = f"TEST-{i+1}"
            
            # Simulate websocket timestamp
            websocket_timestamp = time.time()
            
            # Measure response time
            start_time = time.perf_counter()
            success = self.auto_hold_system.hold_seat(seat_id, websocket_timestamp)
            response_time_ms = (time.perf_counter() - start_time) * 1000
            
            response_times.append(response_time_ms)
            if success:
                successes += 1
            
            # Small delay between tests
            await asyncio.sleep(0.1)
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        result = {
            'test': 'Single Seat Performance',
            'iterations': iterations,
            'successes': successes,
            'success_rate': (successes / iterations) * 100,
            'avg_response_time_ms': avg_response_time,
            'max_response_time_ms': max_response_time,
            'min_response_time_ms': min_response_time,
            'target_met': avg_response_time < 100  # Target: <100ms
        }
        
        self.test_results.append(result)
        return result
    
    async def test_concurrent_performance(self, seat_count: int = 50) -> dict:
        """Test concurrent seat holding performance"""
        logger.info(f"🎯 Testing concurrent performance ({seat_count} seats)")
        
        # Generate test seat IDs
        seat_ids = [f"CONCURRENT-{i+1}" for i in range(seat_count)]
        
        # Measure total time for concurrent submission
        start_time = time.perf_counter()
        success = self.auto_hold_system.hold_multiple_seats(seat_ids)
        submission_time_ms = (time.perf_counter() - start_time) * 1000
        
        # Wait for operations to complete
        await asyncio.sleep(5)
        
        # Get final stats
        stats = self.auto_hold_system.get_performance_stats()
        
        result = {
            'test': 'Concurrent Performance',
            'seat_count': seat_count,
            'submission_success': success,
            'submission_time_ms': submission_time_ms,
            'avg_total_time_ms': stats.get('avg_total_time_ms', 0),
            'success_rate': stats.get('success_rate', 0),
            'target_met': submission_time_ms < 1000  # Target: <1s for 50 seats
        }
        
        self.test_results.append(result)
        return result
    
    async def test_stress_performance(self, seat_count: int = 100) -> dict:
        """Test stress performance with many seats"""
        logger.info(f"🎯 Testing stress performance ({seat_count} seats)")
        
        # Generate test seat IDs
        seat_ids = [f"STRESS-{i+1}" for i in range(seat_count)]
        
        # Submit in batches to avoid overwhelming
        batch_size = 20
        total_submission_time = 0
        
        for i in range(0, len(seat_ids), batch_size):
            batch = seat_ids[i:i+batch_size]
            
            start_time = time.perf_counter()
            self.auto_hold_system.hold_multiple_seats(batch)
            batch_time = (time.perf_counter() - start_time) * 1000
            total_submission_time += batch_time
            
            # Small delay between batches
            await asyncio.sleep(0.1)
        
        # Wait for all operations to complete
        await asyncio.sleep(10)
        
        # Get final stats
        stats = self.auto_hold_system.get_performance_stats()
        
        result = {
            'test': 'Stress Performance',
            'seat_count': seat_count,
            'total_submission_time_ms': total_submission_time,
            'avg_total_time_ms': stats.get('avg_total_time_ms', 0),
            'success_rate': stats.get('success_rate', 0),
            'target_met': total_submission_time < 5000  # Target: <5s for 100 seats
        }
        
        self.test_results.append(result)
        return result
    
    async def run_all_tests(self) -> List[dict]:
        """Run all performance tests"""
        logger.info("🚀 Starting comprehensive auto-hold performance tests")
        
        if not await self.initialize():
            return []
        
        # Wait for token pool to initialize
        logger.info("⏳ Waiting for token pool to initialize...")
        await asyncio.sleep(5)
        
        try:
            # Test 1: Single seat performance
            await self.test_single_seat_performance(10)
            
            # Test 2: Concurrent performance
            await self.test_concurrent_performance(50)
            
            # Test 3: Stress performance
            await self.test_stress_performance(100)
            
            # Final performance summary
            logger.info("📊 Final Performance Summary:")
            log_performance_summary()
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Test execution failed: {str(e)}")
            return self.test_results
        
        finally:
            if self.auto_hold_system:
                self.auto_hold_system.cleanup()
    
    def print_results(self):
        """Print detailed test results"""
        logger.info("📊 AUTO-HOLD PERFORMANCE TEST RESULTS")
        logger.info("=" * 60)
        
        for result in self.test_results:
            test_name = result['test']
            target_met = "✅ PASS" if result['target_met'] else "❌ FAIL"
            
            logger.info(f"\n🎯 {test_name}: {target_met}")
            
            for key, value in result.items():
                if key not in ['test', 'target_met']:
                    if isinstance(value, float):
                        logger.info(f"   {key}: {value:.2f}")
                    else:
                        logger.info(f"   {key}: {value}")
        
        logger.info("\n📈 SUMMARY:")
        logger.info(f"   Total held seats: {len(self.held_seats)}")
        logger.info(f"   Total failed seats: {len(self.failed_seats)}")
        
        if self.failed_seats:
            logger.info("❌ Failed seats:")
            for seat_id, error in self.failed_seats[:5]:  # Show first 5 failures
                logger.info(f"   {seat_id}: {error}")

async def main():
    """Main test function"""
    # You need to provide a valid event URL for testing
    event_url = "https://webook.com/events/your-event-url"  # Replace with actual URL
    proxy = None  # Set proxy if needed
    
    test_system = AutoHoldPerformanceTest(event_url, proxy)
    results = await test_system.run_all_tests()
    test_system.print_results()

if __name__ == "__main__":
    print("🎯 Auto-Hold Performance Test")
    print("To run tests, update the event_url in main() and run:")
    print("python test_auto_hold_performance.py")
    print("\nOr import this module and use AutoHoldPerformanceTest class directly.")
