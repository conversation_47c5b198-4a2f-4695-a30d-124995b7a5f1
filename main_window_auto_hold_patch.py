"""
Main Window Auto-Hold Patch
This file shows how to replace the existing auto-hold logic in main_window.py
with the new optimized auto_hold.py system.
"""

import time
import logging
from typing import Optional

from auto_hold import initialize_auto_hold, get_auto_hold_system, hold_seat_fast

logger = logging.getLogger("webook_pro")

class MainWindowAutoHoldPatch:
    """
    Patch class to replace existing auto-hold functionality in main_window.py
    """
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.auto_hold_system = None
        self.held_seats = set()
        self.pending_seats = set()
        
        # Replace the existing auto_held_seats tracking
        self.main_window.auto_held_seats = {}
        self.main_window.pending_seats = 0
    
    def initialize_auto_hold(self, event_key: str, channel_keys: list, team_id: Optional[str] = None, proxy: Optional[str] = None, tickets_wanted: int = 10):
        """Initialize the auto-hold system when event is loaded"""
        try:
            self.auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)

            # Set target tickets for token pre-caching
            self.auto_hold_system.set_target_tickets(tickets_wanted)

            # Set up callbacks
            self.auto_hold_system.set_callbacks(
                success_callback=self._on_seat_held_success,
                failure_callback=self._on_seat_held_failure
            )

            # Wait for enough tokens to be ready
            logger.info(f"⏳ Waiting for {tickets_wanted} tokens to be ready...")
            if self.auto_hold_system.wait_for_tokens(tickets_wanted, timeout=30):
                logger.info(f"🚀 Auto-hold system ready for {tickets_wanted} tickets on event {event_key}")
                return True
            else:
                logger.warning(f"⚠️ Auto-hold system initialized but only {len(self.auto_hold_system.token_pool.tokens)} tokens ready")
                return True  # Still return True, system will work with available tokens

        except Exception as e:
            logger.error(f"❌ Failed to initialize auto-hold system: {str(e)}")
            return False
    
    def _on_seat_held_success(self, seat_id: str, token: str):
        """Callback when seat is successfully held"""
        # Update tracking
        self.held_seats.add(seat_id)
        self.pending_seats.discard(seat_id)
        
        # Update main window tracking
        with self.main_window.data_lock:
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
        
        # Log success
        logger.info(f"✅ Auto-held seat {seat_id} with token {token[:8]}...")
        
        # Update UI if needed
        if hasattr(self.main_window, 'held_seats_tab'):
            self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        
        # Update window title
        self.main_window._update_window_title()
    
    def _on_seat_held_failure(self, seat_id: str, error: str):
        """Callback when seat hold fails"""
        # Update tracking
        self.pending_seats.discard(seat_id)
        
        # Update main window tracking
        with self.main_window.data_lock:
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
        
        # Log failure with detailed error
        logger.error(f"❌ Failed to auto-hold seat {seat_id}: {error}")
        
        # Update window title
        self.main_window._update_window_title()
    
    def on_seat_data_updated(self, seat_data: dict):
        """
        Ultra-fast seat update handler - replaces main_window._on_seat_data_updated
        This is the critical performance path that must complete in <1ms
        """
        if not self.auto_hold_system:
            return
        
        seat_id = seat_data.get('objectLabelOrUuid')
        if not seat_id:
            return
        
        # CRITICAL PATH - Minimal overhead checks
        
        # Skip if already being processed
        if seat_id in self.held_seats or seat_id in self.pending_seats:
            return
        
        # Skip if already in main window tracking
        if seat_id in self.main_window.auto_held_seats:
            return
        
        # Check if auto-hold is enabled (use existing main window logic)
        if not getattr(self.main_window, 'auto_hold_enabled', False):
            return
        
        # Check seat type filtering (use existing main window logic)
        if hasattr(self.main_window, '_cached_selected_types'):
            if '*' not in self.main_window._cached_selected_types:
                seat_type = seat_id.partition('-')[0]
                if seat_type not in self.main_window._cached_selected_types:
                    return
        
        # Check seat status - only hold free seats
        status = seat_data.get('status')
        if status != 'free':
            return
        
        # Extract websocket timestamp for accurate performance measurement
        websocket_timestamp = seat_data.get('_websocket_timestamp', time.time())
        
        # Update tracking atomically
        with self.main_window.data_lock:
            # Double-check to avoid race conditions
            if seat_id in self.main_window.auto_held_seats:
                return
            
            # Add to tracking
            self.main_window.auto_held_seats[seat_id] = websocket_timestamp
            self.main_window.pending_seats += 1
            self.pending_seats.add(seat_id)
        
        # CRITICAL: This must complete in <1ms
        success = hold_seat_fast(seat_id, websocket_timestamp)
        
        if not success:
            # Remove from tracking if submission failed
            with self.main_window.data_lock:
                self.main_window.auto_held_seats.pop(seat_id, None)
                self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
            self.pending_seats.discard(seat_id)
            
            logger.warning(f"⚠️ Failed to submit auto-hold for seat {seat_id}")
    
    def get_performance_stats(self) -> dict:
        """Get current performance statistics"""
        stats = {
            'held_seats': len(self.held_seats),
            'pending_seats': len(self.pending_seats),
            'total_seats': len(self.held_seats) + len(self.pending_seats)
        }

        if self.auto_hold_system:
            auto_stats = self.auto_hold_system.get_performance_stats()
            stats.update(auto_stats)

            # Add token pool stats
            with self.auto_hold_system.token_pool.lock:
                stats['available_tokens'] = len(self.auto_hold_system.token_pool.tokens)
                stats['target_tokens'] = self.auto_hold_system.token_pool.pool_size
                stats['target_tickets'] = self.auto_hold_system.token_pool.target_tickets

        return stats

    def update_tickets_wanted(self, tickets_wanted: int):
        """Update the number of tickets wanted and adjust token pool"""
        if self.auto_hold_system:
            self.auto_hold_system.set_target_tickets(tickets_wanted)
            logger.info(f"🎯 Updated target to {tickets_wanted} tickets")

    def hold_multiple_seats_now(self, seat_ids: list) -> bool:
        """Hold multiple specific seats immediately"""
        if not self.auto_hold_system:
            return False

        # Filter out already held/pending seats
        seats_to_hold = [
            seat_id for seat_id in seat_ids
            if seat_id not in self.held_seats and seat_id not in self.pending_seats
        ]

        if not seats_to_hold:
            return True

        # Check if we have enough tokens
        required_tokens = len(seats_to_hold)
        available_tokens = len(self.auto_hold_system.token_pool.tokens)

        if available_tokens < required_tokens:
            logger.warning(f"⚠️ Only {available_tokens} tokens available for {required_tokens} seats")

        # Add to pending
        self.pending_seats.update(seats_to_hold)

        # Update main window tracking
        with self.main_window.data_lock:
            for seat_id in seats_to_hold:
                self.main_window.auto_held_seats[seat_id] = time.time()
                self.main_window.pending_seats += 1

        # Submit holds
        from auto_hold import hold_multiple_seats_fast
        success = hold_multiple_seats_fast(seats_to_hold)

        if success:
            logger.info(f"🚀 Submitted {len(seats_to_hold)} concurrent hold requests")
        else:
            # Remove from tracking if submission failed
            self.pending_seats -= set(seats_to_hold)
            with self.main_window.data_lock:
                for seat_id in seats_to_hold:
                    self.main_window.auto_held_seats.pop(seat_id, None)
                    self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)

        return success
    
    def log_performance_summary(self):
        """Log detailed performance summary"""
        stats = self.get_performance_stats()
        
        logger.info("📊 AUTO-HOLD PERFORMANCE SUMMARY")
        logger.info(f"   🎯 Held Seats: {stats['held_seats']}")
        logger.info(f"   ⏳ Pending Seats: {stats['pending_seats']}")
        logger.info(f"   📈 Success Rate: {stats.get('success_rate', 0):.1f}%")
        logger.info(f"   ⏱️  Avg Response Time: {stats.get('avg_total_time_ms', 0):.2f}ms")
        
        if self.auto_hold_system:
            self.auto_hold_system.log_performance_summary()
    
    def reset_auto_held_seats(self):
        """Reset auto-held seats tracking"""
        with self.main_window.data_lock:
            self.main_window.auto_held_seats = {}
            self.main_window.pending_seats = 0
        
        self.held_seats.clear()
        self.pending_seats.clear()
        
        logger.info("🔄 Auto-held seats tracking reset")
    
    def cleanup(self):
        """Clean up resources"""
        if self.auto_hold_system:
            self.auto_hold_system.cleanup()
        
        self.held_seats.clear()
        self.pending_seats.clear()

def patch_main_window(main_window):
    """
    Apply the auto-hold patch to an existing main window instance
    
    Usage:
        from main_window_auto_hold_patch import patch_main_window
        
        # In your main window initialization:
        auto_hold_patch = patch_main_window(self)
        
        # When event is loaded:
        auto_hold_patch.initialize_auto_hold(event_key, channel_keys, team_id, proxy)
        
        # Replace the websocket handler:
        websocket_manager.seat_data_updated.connect(auto_hold_patch.on_seat_data_updated)
    """
    
    # Create patch instance
    patch = MainWindowAutoHoldPatch(main_window)
    
    # Store reference in main window
    main_window.auto_hold_patch = patch
    
    # Replace existing methods
    main_window._original_on_seat_data_updated = getattr(main_window, '_on_seat_data_updated', None)
    main_window._on_seat_data_updated = patch.on_seat_data_updated
    
    # Add new methods
    main_window.initialize_auto_hold = patch.initialize_auto_hold
    main_window.get_auto_hold_stats = patch.get_performance_stats
    main_window.log_auto_hold_summary = patch.log_performance_summary
    main_window.reset_auto_held_seats_new = patch.reset_auto_held_seats
    
    logger.info("🔧 Main window patched with optimized auto-hold system")
    
    return patch

# Example usage in main_window.py:
"""
# Add this to your main_window.py imports:
from main_window_auto_hold_patch import patch_main_window

# In your MainWindow.__init__ method, after other initialization:
self.auto_hold_patch = patch_main_window(self)

# In your event loading method:
def load_event(self, event_key, channel_keys, team_id, proxy=None):
    # ... existing event loading code ...

    # Get tickets wanted from UI (example)
    tickets_wanted = int(self.tickets_wanted_spinbox.value())  # or however you get this value

    # Initialize auto-hold system with tickets wanted
    self.auto_hold_patch.initialize_auto_hold(event_key, channel_keys, team_id, proxy, tickets_wanted)

# When user changes tickets wanted:
def on_tickets_wanted_changed(self, new_value):
    self.auto_hold_patch.update_tickets_wanted(new_value)

# In your websocket connection setup:
def setup_websocket(self):
    # ... existing websocket setup ...

    # Connect to the new optimized handler
    self.websocket_manager.seat_data_updated.connect(self.auto_hold_patch.on_seat_data_updated)

# To hold specific seats immediately:
def hold_specific_seats(self, seat_ids):
    success = self.auto_hold_patch.hold_multiple_seats_now(seat_ids)
    if success:
        self.log(f"🚀 Holding {len(seat_ids)} seats concurrently")
    else:
        self.log("❌ Failed to submit hold requests")

# Add a performance logging timer (optional):
def setup_performance_logging(self):
    self.performance_timer = QTimer()
    self.performance_timer.timeout.connect(self.auto_hold_patch.log_performance_summary)
    self.performance_timer.start(30000)  # Log every 30 seconds

# Check token status:
def check_token_status(self):
    stats = self.auto_hold_patch.get_performance_stats()
    self.log(f"🔑 Tokens: {stats.get('available_tokens', 0)}/{stats.get('target_tokens', 0)} "
             f"for {stats.get('target_tickets', 0)} tickets")
"""
