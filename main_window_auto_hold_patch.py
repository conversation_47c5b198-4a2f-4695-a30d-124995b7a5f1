"""
Main Window Auto-Hold Patch
This file shows how to replace the existing auto-hold logic in main_window.py
with the new optimized auto_hold.py system.
"""

import time
import logging
from typing import Optional

from auto_hold import initialize_auto_hold, get_auto_hold_system, hold_seat_fast

logger = logging.getLogger("webook_pro")

class MainWindowAutoHoldPatch:
    """
    Patch class to replace existing auto-hold functionality in main_window.py
    """
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.auto_hold_system = None
        self.held_seats = set()
        self.pending_seats = set()
        
        # Replace the existing auto_held_seats tracking
        self.main_window.auto_held_seats = {}
        self.main_window.pending_seats = 0
    
    def initialize_auto_hold(self, event_key: str, channel_keys: list, team_id: Optional[str] = None, proxy: Optional[str] = None):
        """Initialize the auto-hold system when event is loaded"""
        try:
            self.auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)
            
            # Set up callbacks
            self.auto_hold_system.set_callbacks(
                success_callback=self._on_seat_held_success,
                failure_callback=self._on_seat_held_failure
            )
            
            logger.info(f"🚀 Auto-hold system initialized for event {event_key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize auto-hold system: {str(e)}")
            return False
    
    def _on_seat_held_success(self, seat_id: str, token: str):
        """Callback when seat is successfully held"""
        # Update tracking
        self.held_seats.add(seat_id)
        self.pending_seats.discard(seat_id)
        
        # Update main window tracking
        with self.main_window.data_lock:
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
        
        # Log success
        logger.info(f"✅ Auto-held seat {seat_id} with token {token[:8]}...")
        
        # Update UI if needed
        if hasattr(self.main_window, 'held_seats_tab'):
            self.main_window.held_seats_tab.auto_refresh_held_seats(force_rebuild=True)
        
        # Update window title
        self.main_window._update_window_title()
    
    def _on_seat_held_failure(self, seat_id: str, error: str):
        """Callback when seat hold fails"""
        # Update tracking
        self.pending_seats.discard(seat_id)
        
        # Update main window tracking
        with self.main_window.data_lock:
            self.main_window.auto_held_seats.pop(seat_id, None)
            self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
        
        # Log failure with detailed error
        logger.error(f"❌ Failed to auto-hold seat {seat_id}: {error}")
        
        # Update window title
        self.main_window._update_window_title()
    
    def on_seat_data_updated(self, seat_data: dict):
        """
        Ultra-fast seat update handler - replaces main_window._on_seat_data_updated
        This is the critical performance path that must complete in <1ms
        """
        if not self.auto_hold_system:
            return
        
        seat_id = seat_data.get('objectLabelOrUuid')
        if not seat_id:
            return
        
        # CRITICAL PATH - Minimal overhead checks
        
        # Skip if already being processed
        if seat_id in self.held_seats or seat_id in self.pending_seats:
            return
        
        # Skip if already in main window tracking
        if seat_id in self.main_window.auto_held_seats:
            return
        
        # Check if auto-hold is enabled (use existing main window logic)
        if not getattr(self.main_window, 'auto_hold_enabled', False):
            return
        
        # Check seat type filtering (use existing main window logic)
        if hasattr(self.main_window, '_cached_selected_types'):
            if '*' not in self.main_window._cached_selected_types:
                seat_type = seat_id.partition('-')[0]
                if seat_type not in self.main_window._cached_selected_types:
                    return
        
        # Check seat status - only hold free seats
        status = seat_data.get('status')
        if status != 'free':
            return
        
        # Extract websocket timestamp for accurate performance measurement
        websocket_timestamp = seat_data.get('_websocket_timestamp', time.time())
        
        # Update tracking atomically
        with self.main_window.data_lock:
            # Double-check to avoid race conditions
            if seat_id in self.main_window.auto_held_seats:
                return
            
            # Add to tracking
            self.main_window.auto_held_seats[seat_id] = websocket_timestamp
            self.main_window.pending_seats += 1
            self.pending_seats.add(seat_id)
        
        # CRITICAL: This must complete in <1ms
        success = hold_seat_fast(seat_id, websocket_timestamp)
        
        if not success:
            # Remove from tracking if submission failed
            with self.main_window.data_lock:
                self.main_window.auto_held_seats.pop(seat_id, None)
                self.main_window.pending_seats = max(0, self.main_window.pending_seats - 1)
            self.pending_seats.discard(seat_id)
            
            logger.warning(f"⚠️ Failed to submit auto-hold for seat {seat_id}")
    
    def get_performance_stats(self) -> dict:
        """Get current performance statistics"""
        stats = {
            'held_seats': len(self.held_seats),
            'pending_seats': len(self.pending_seats),
            'total_seats': len(self.held_seats) + len(self.pending_seats)
        }
        
        if self.auto_hold_system:
            auto_stats = self.auto_hold_system.get_performance_stats()
            stats.update(auto_stats)
        
        return stats
    
    def log_performance_summary(self):
        """Log detailed performance summary"""
        stats = self.get_performance_stats()
        
        logger.info("📊 AUTO-HOLD PERFORMANCE SUMMARY")
        logger.info(f"   🎯 Held Seats: {stats['held_seats']}")
        logger.info(f"   ⏳ Pending Seats: {stats['pending_seats']}")
        logger.info(f"   📈 Success Rate: {stats.get('success_rate', 0):.1f}%")
        logger.info(f"   ⏱️  Avg Response Time: {stats.get('avg_total_time_ms', 0):.2f}ms")
        
        if self.auto_hold_system:
            self.auto_hold_system.log_performance_summary()
    
    def reset_auto_held_seats(self):
        """Reset auto-held seats tracking"""
        with self.main_window.data_lock:
            self.main_window.auto_held_seats = {}
            self.main_window.pending_seats = 0
        
        self.held_seats.clear()
        self.pending_seats.clear()
        
        logger.info("🔄 Auto-held seats tracking reset")
    
    def cleanup(self):
        """Clean up resources"""
        if self.auto_hold_system:
            self.auto_hold_system.cleanup()
        
        self.held_seats.clear()
        self.pending_seats.clear()

def patch_main_window(main_window):
    """
    Apply the auto-hold patch to an existing main window instance
    
    Usage:
        from main_window_auto_hold_patch import patch_main_window
        
        # In your main window initialization:
        auto_hold_patch = patch_main_window(self)
        
        # When event is loaded:
        auto_hold_patch.initialize_auto_hold(event_key, channel_keys, team_id, proxy)
        
        # Replace the websocket handler:
        websocket_manager.seat_data_updated.connect(auto_hold_patch.on_seat_data_updated)
    """
    
    # Create patch instance
    patch = MainWindowAutoHoldPatch(main_window)
    
    # Store reference in main window
    main_window.auto_hold_patch = patch
    
    # Replace existing methods
    main_window._original_on_seat_data_updated = getattr(main_window, '_on_seat_data_updated', None)
    main_window._on_seat_data_updated = patch.on_seat_data_updated
    
    # Add new methods
    main_window.initialize_auto_hold = patch.initialize_auto_hold
    main_window.get_auto_hold_stats = patch.get_performance_stats
    main_window.log_auto_hold_summary = patch.log_performance_summary
    main_window.reset_auto_held_seats_new = patch.reset_auto_held_seats
    
    logger.info("🔧 Main window patched with optimized auto-hold system")
    
    return patch

# Example usage in main_window.py:
"""
# Add this to your main_window.py imports:
from main_window_auto_hold_patch import patch_main_window

# In your MainWindow.__init__ method, after other initialization:
self.auto_hold_patch = patch_main_window(self)

# In your event loading method:
def load_event(self, event_key, channel_keys, team_id, proxy=None):
    # ... existing event loading code ...
    
    # Initialize auto-hold system
    self.auto_hold_patch.initialize_auto_hold(event_key, channel_keys, team_id, proxy)

# In your websocket connection setup:
def setup_websocket(self):
    # ... existing websocket setup ...
    
    # Connect to the new optimized handler
    self.websocket_manager.seat_data_updated.connect(self.auto_hold_patch.on_seat_data_updated)

# Add a performance logging timer (optional):
def setup_performance_logging(self):
    self.performance_timer = QTimer()
    self.performance_timer.timeout.connect(self.auto_hold_patch.log_performance_summary)
    self.performance_timer.start(30000)  # Log every 30 seconds
"""
