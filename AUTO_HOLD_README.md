# Auto Hold System - Ultra-High Performance Seat Holding

This is a single, optimized auto-hold system designed to hold seats within 100ms with detailed performance benchmarking. It replaces all existing auto-hold systems (`fast_hold.py`, `ultra_fast_hold_manager.py`, `lightning_fast_hold_manager.py`, `simple_hold_manager.py`) with one clean, efficient solution.

## Key Features

- **Sub-100ms Response Time**: Optimized for ultra-fast seat holding
- **Concurrent Support**: Handle up to 1000 seats simultaneously
- **Detailed Performance Tracking**: Comprehensive benchmarking of every operation
- **Pre-cached Tokens**: Token pool management for instant availability
- **Minimal Overhead**: Single-file solution with clean architecture
- **Real-time Monitoring**: Detailed logs of performance bottlenecks

## Files

- `auto_hold.py` - Main auto-hold system (single file solution)
- `auto_hold_integration.py` - Integration examples and helpers
- `main_window_auto_hold_patch.py` - Drop-in replacement for main_window.py
- `test_auto_hold_performance.py` - Performance testing and validation
- `AUTO_HOLD_README.md` - This documentation

## Performance Targets

- **Auto-hold trigger**: <1ms from websocket message to hold submission
- **Total hold time**: <100ms from websocket message to seat held
- **Concurrent holds**: 1000 seats in <1 second
- **Token availability**: 50 pre-cached tokens ready at all times

## Detailed Performance Tracking

The system tracks and logs:

1. **WebSocket → Decision**: Time from receiving websocket message to deciding to hold
2. **Decision → Request**: Time from decision to sending HTTP request
3. **Request → Response**: Network time for the hold request
4. **Total Time**: Complete end-to-end time

Example log output:
```
🎯 HOLD PERFORMANCE [A-15] ✅ SUCCESS
   📊 WS→Decision: 0.12ms | Decision→Request: 0.85ms | Request→Response: 45.23ms | TOTAL: 46.20ms
```

## Quick Integration

### Option 1: Patch Existing Main Window

```python
from main_window_auto_hold_patch import patch_main_window

# In your MainWindow.__init__:
self.auto_hold_patch = patch_main_window(self)

# When event loads:
self.auto_hold_patch.initialize_auto_hold(event_key, channel_keys, team_id, proxy)

# Connect websocket:
websocket_manager.seat_data_updated.connect(self.auto_hold_patch.on_seat_data_updated)
```

### Option 2: Direct Integration

```python
from auto_hold import initialize_auto_hold, hold_seat_fast

# Initialize system
auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)

# Set callbacks
auto_hold_system.set_callbacks(
    success_callback=lambda seat_id, token: print(f"Held {seat_id}"),
    failure_callback=lambda seat_id, error: print(f"Failed {seat_id}: {error}")
)

# In websocket handler:
def on_websocket_message(seat_data):
    seat_id = seat_data['objectLabelOrUuid']
    websocket_timestamp = seat_data.get('_websocket_timestamp', time.time())
    
    # This completes in <1ms
    hold_seat_fast(seat_id, websocket_timestamp)
```

## Performance Testing

Run comprehensive performance tests:

```python
from test_auto_hold_performance import AutoHoldPerformanceTest

# Update event URL in the test file
test_system = AutoHoldPerformanceTest("https://webook.com/events/your-event")
results = await test_system.run_all_tests()
test_system.print_results()
```

## Architecture

### Token Pool Management
- Pre-caches 50 tokens for instant availability
- Background refresh to maintain pool
- Automatic expiry handling

### HTTP Client Pool
- Reuses HTTP connections for maximum performance
- Concurrent request handling
- Automatic connection management

### Performance Tracking
- Real-time operation monitoring
- Detailed timing breakdowns
- Success/failure rate tracking
- Bottleneck identification

### Thread Pool Execution
- Non-blocking seat hold submissions
- Concurrent processing of up to 1000 seats
- Automatic resource management

## Configuration

Key constants in `auto_hold.py`:

```python
MAX_CONCURRENT_HOLDS = 1000    # Maximum concurrent seat holds
TOKEN_POOL_SIZE = 50           # Number of pre-cached tokens
TOKEN_REFRESH_THRESHOLD = 300  # Token refresh time (5 minutes)
```

## Monitoring and Debugging

### Real-time Stats

```python
from auto_hold import get_performance_stats, log_performance_summary

# Get current statistics
stats = get_performance_stats()
print(f"Success rate: {stats['success_rate']:.1f}%")
print(f"Average time: {stats['avg_total_time_ms']:.2f}ms")

# Log detailed summary
log_performance_summary()
```

### Performance Logs

The system automatically logs detailed performance information:

```
📊 AUTO-HOLD PERFORMANCE SUMMARY
   🎯 Total Attempts: 150
   ✅ Successes: 147 (98.0%)
   ❌ Failures: 3 (Token: 1, Network: 2)
   ⏱️  Average Total Time: 52.34ms
   📡 WebSocket→Decision: 0.15ms
   🔄 Decision→Request: 1.23ms
   🌐 Request→Response: 50.96ms
   🔄 Active Operations: 0
```

## Migration from Existing Systems

To replace existing auto-hold systems:

1. **Remove old files**: Delete `fast_hold.py`, `ultra_fast_hold_manager.py`, etc.
2. **Update imports**: Replace old imports with `from auto_hold import ...`
3. **Apply patch**: Use `main_window_auto_hold_patch.py` for easy migration
4. **Test performance**: Run `test_auto_hold_performance.py` to validate

## Troubleshooting

### Common Issues

1. **Slow performance**: Check token pool status and network latency
2. **High failure rate**: Verify event configuration and token generation
3. **Memory usage**: Monitor HTTP client pool and active operations

### Debug Logging

Enable detailed logging:

```python
import logging
logging.getLogger("webook_pro").setLevel(logging.DEBUG)
```

### Performance Analysis

Use the built-in performance tracker to identify bottlenecks:

```python
stats = get_performance_stats()
if stats['avg_total_time_ms'] > 100:
    print("Performance target not met!")
    print(f"Network time: {stats['avg_request_to_response_ms']:.2f}ms")
    print(f"Processing time: {stats['avg_decision_to_request_ms']:.2f}ms")
```

## Benefits Over Previous Systems

1. **Single File**: No more multiple "ultra-fast" and "lightning-fast" systems
2. **Detailed Metrics**: Know exactly where time is spent
3. **Optimized Architecture**: Purpose-built for <100ms performance
4. **Clean Integration**: Drop-in replacement with minimal changes
5. **Scalable**: Handles up to 1000 concurrent seats efficiently
6. **Maintainable**: Clean, well-documented code structure

## Support

For issues or questions:
1. Check the performance logs for detailed timing information
2. Run the test suite to validate system performance
3. Review the integration examples for proper usage patterns
