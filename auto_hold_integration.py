"""
Auto Hold Integration Example
Shows how to integrate the new auto_hold.py system with existing websocket handling
"""

import logging
import time
from typing import Optional, List

from auto_hold import (
    initialize_auto_hold, 
    get_auto_hold_system, 
    hold_seat_fast, 
    hold_multiple_seats_fast,
    log_performance_summary
)

logger = logging.getLogger("webook_pro")

class AutoHoldIntegration:
    """Integration layer for the auto-hold system"""
    
    def __init__(self):
        self.auto_hold_system = None
        self.held_seats = set()
        self.pending_seats = set()
        
    def initialize(self, event_key: str, channel_keys: List[str], team_id: Optional[str] = None, proxy: Optional[str] = None):
        """Initialize the auto-hold system"""
        self.auto_hold_system = initialize_auto_hold(event_key, channel_keys, team_id, proxy)
        
        # Set up callbacks
        self.auto_hold_system.set_callbacks(
            success_callback=self._on_seat_held,
            failure_callback=self._on_seat_hold_failed
        )
        
        logger.info(f"🚀 Auto-hold integration initialized for event {event_key}")
    
    def _on_seat_held(self, seat_id: str, token: str):
        """Callback when a seat is successfully held"""
        self.held_seats.add(seat_id)
        self.pending_seats.discard(seat_id)
        logger.info(f"✅ Successfully held seat {seat_id} with token {token[:8]}...")
    
    def _on_seat_hold_failed(self, seat_id: str, error: str):
        """Callback when a seat hold fails"""
        self.pending_seats.discard(seat_id)
        logger.error(f"❌ Failed to hold seat {seat_id}: {error}")
    
    def on_websocket_seat_update(self, seat_data: dict):
        """
        Handle websocket seat updates - this is the main entry point
        Call this from your websocket message handler
        """
        if not self.auto_hold_system:
            return
        
        seat_id = seat_data.get('objectLabelOrUuid')
        if not seat_id:
            return
        
        # Skip if already held or pending
        if seat_id in self.held_seats or seat_id in self.pending_seats:
            return
        
        # Check if seat is available (customize this logic as needed)
        status = seat_data.get('status')
        if status != 'free':  # Only hold free seats
            return
        
        # Extract websocket timestamp for accurate performance measurement
        websocket_timestamp = seat_data.get('_websocket_timestamp', time.time())
        
        # Add to pending and trigger hold
        self.pending_seats.add(seat_id)
        
        # This is the critical path - should complete in <1ms
        success = hold_seat_fast(seat_id, websocket_timestamp)
        
        if not success:
            self.pending_seats.discard(seat_id)
            logger.warning(f"⚠️ Failed to submit hold request for seat {seat_id}")
    
    def hold_multiple_seats_now(self, seat_ids: List[str]) -> bool:
        """Hold multiple seats immediately"""
        if not self.auto_hold_system:
            return False
        
        # Filter out already held/pending seats
        seats_to_hold = [
            seat_id for seat_id in seat_ids 
            if seat_id not in self.held_seats and seat_id not in self.pending_seats
        ]
        
        if not seats_to_hold:
            return True
        
        # Add to pending
        self.pending_seats.update(seats_to_hold)
        
        # Submit holds
        return hold_multiple_seats_fast(seats_to_hold)
    
    def get_stats(self) -> dict:
        """Get current statistics"""
        stats = {
            'held_seats': len(self.held_seats),
            'pending_seats': len(self.pending_seats),
            'total_seats': len(self.held_seats) + len(self.pending_seats)
        }
        
        if self.auto_hold_system:
            stats.update(self.auto_hold_system.get_performance_stats())
        
        return stats
    
    def log_summary(self):
        """Log performance summary"""
        stats = self.get_stats()
        logger.info(f"📊 SEAT SUMMARY: Held: {stats['held_seats']}, Pending: {stats['pending_seats']}")
        
        if self.auto_hold_system:
            log_performance_summary()
    
    def cleanup(self):
        """Clean up resources"""
        if self.auto_hold_system:
            self.auto_hold_system.cleanup()
        
        self.held_seats.clear()
        self.pending_seats.clear()

# Example usage for main_window.py integration
def integrate_with_main_window(main_window):
    """
    Example of how to integrate with main_window.py
    Replace the existing auto-hold logic with this
    """
    
    # Create integration instance
    auto_hold_integration = AutoHoldIntegration()
    
    # Initialize when event is loaded
    def on_event_loaded(event_key, channel_keys, team_id, proxy=None):
        auto_hold_integration.initialize(event_key, channel_keys, team_id, proxy)
    
    # Replace the existing _on_seat_data_updated method
    def new_on_seat_data_updated(seat_data):
        """
        Ultra-fast seat update handler - replaces existing method
        """
        # This should complete in <1ms for maximum performance
        auto_hold_integration.on_websocket_seat_update(seat_data)
    
    # Add performance logging timer
    def log_performance_periodically():
        auto_hold_integration.log_summary()
        # Schedule next log in 30 seconds
        main_window.timer = main_window.startTimer(30000)
    
    return auto_hold_integration

# Example standalone usage
if __name__ == "__main__":
    import asyncio
    
    async def test_auto_hold():
        """Test the auto-hold system"""
        
        # Initialize
        integration = AutoHoldIntegration()
        integration.initialize(
            event_key="your_event_key",
            channel_keys=["your_channel_key"],
            team_id="your_team_id"
        )
        
        # Simulate websocket messages
        test_seats = [f"A-{i}" for i in range(1, 11)]
        
        for seat_id in test_seats:
            seat_data = {
                'objectLabelOrUuid': seat_id,
                'status': 'free',
                '_websocket_timestamp': time.time()
            }
            integration.on_websocket_seat_update(seat_data)
        
        # Wait a bit for operations to complete
        await asyncio.sleep(2)
        
        # Log results
        integration.log_summary()
        
        # Cleanup
        integration.cleanup()
    
    # Run test
    # asyncio.run(test_auto_hold())
    print("Auto-hold integration ready. Import and use integrate_with_main_window() to integrate.")
