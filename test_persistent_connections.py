#!/usr/bin/env python3
"""
Test Persistent Connections Performance
Validates that the new persistent connection system achieves minimal overhead
"""

import time
import logging
from auto_hold import PersistentConnection, ConnectionPool

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_persistent_connection_speed():
    """Test the speed of persistent connections vs regular HTTP"""
    
    logger.info("🚀 Testing Persistent Connection Performance")
    
    # Test persistent connection
    logger.info("📡 Testing persistent connection...")
    
    conn = PersistentConnection("*************", 443, True)
    
    if not conn.connected:
        logger.error("❌ Failed to establish persistent connection")
        return
    
    # Test multiple requests on same connection
    request_times = []
    
    headers = {
        'Host': 'cdn-eu.seatsio.net',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*'
    }
    
    for i in range(10):
        start_time = time.perf_counter()
        
        # Simple GET request to test connection speed
        status_code, response = conn.send_request('GET', '/chart.js', headers)
        
        end_time = time.perf_counter()
        request_time_ms = (end_time - start_time) * 1000
        request_times.append(request_time_ms)
        
        logger.info(f"   Request {i+1}: {request_time_ms:.2f}ms (Status: {status_code})")
        
        time.sleep(0.1)  # Small delay between requests
    
    avg_time = sum(request_times) / len(request_times)
    min_time = min(request_times)
    max_time = max(request_times)
    
    logger.info(f"📊 Persistent Connection Results:")
    logger.info(f"   Average: {avg_time:.2f}ms")
    logger.info(f"   Minimum: {min_time:.2f}ms")
    logger.info(f"   Maximum: {max_time:.2f}ms")
    
    conn.close()
    
    return avg_time

def test_connection_pool():
    """Test connection pool performance"""
    
    logger.info("🔗 Testing Connection Pool Performance")
    
    pool = ConnectionPool("*************", pool_size=5)
    
    if not pool.connections:
        logger.error("❌ Failed to create connection pool")
        return
    
    logger.info(f"✅ Created pool with {len(pool.connections)} connections")
    
    # Test getting and returning connections
    get_times = []
    
    for i in range(20):
        start_time = time.perf_counter()
        
        conn = pool.get_connection()
        if conn:
            pool.return_connection(conn)
        
        end_time = time.perf_counter()
        get_time_ms = (end_time - start_time) * 1000
        get_times.append(get_time_ms)
        
        logger.info(f"   Get/Return {i+1}: {get_time_ms:.4f}ms")
    
    avg_get_time = sum(get_times) / len(get_times)
    logger.info(f"📊 Connection Pool Results:")
    logger.info(f"   Average Get/Return: {avg_get_time:.4f}ms")
    
    pool.cleanup()
    
    return avg_get_time

def test_hold_request_simulation():
    """Simulate the actual hold request to measure minimal overhead"""
    
    logger.info("🎯 Testing Hold Request Simulation")
    
    conn = PersistentConnection("*************", 443, True)
    
    if not conn.connected:
        logger.error("❌ Failed to establish connection for hold test")
        return
    
    # Simulate hold request data
    headers = {
        'Host': 'cdn-eu.seatsio.net',
        'accept': '*/*',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'x-client-tool': 'Renderer',
        'x-browser-id': 'test12345',
        'x-signature': 'test_signature'
    }
    
    body = '{"events":["test"],"holdToken":"test","objects":[{"objectId":"A-1"}],"channelKeys":["NO_CHANNEL"],"validateEventsLinkedToSameChart":true}'
    
    # Test request timing
    request_times = []
    
    for i in range(5):
        start_time = time.perf_counter()
        
        # This would be the actual hold request
        path = '/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        status_code, response = conn.send_request('POST', path, headers, body)
        
        end_time = time.perf_counter()
        request_time_ms = (end_time - start_time) * 1000
        request_times.append(request_time_ms)
        
        logger.info(f"   Hold Request {i+1}: {request_time_ms:.2f}ms (Status: {status_code})")
        
        time.sleep(0.5)  # Delay between hold attempts
    
    avg_time = sum(request_times) / len(request_times)
    logger.info(f"📊 Hold Request Results:")
    logger.info(f"   Average Hold Time: {avg_time:.2f}ms")
    
    conn.close()
    
    return avg_time

def main():
    """Run all performance tests"""
    
    logger.info("🚀 PERSISTENT CONNECTION PERFORMANCE TESTS")
    logger.info("=" * 60)
    
    try:
        # Test 1: Basic persistent connection
        persistent_time = test_persistent_connection_speed()
        
        print()
        
        # Test 2: Connection pool
        pool_time = test_connection_pool()
        
        print()
        
        # Test 3: Hold request simulation
        hold_time = test_hold_request_simulation()
        
        print()
        
        # Summary
        logger.info("📈 PERFORMANCE SUMMARY")
        logger.info("=" * 40)
        logger.info(f"🔗 Persistent Connection Avg: {persistent_time:.2f}ms")
        logger.info(f"🏊 Connection Pool Get/Return: {pool_time:.4f}ms")
        logger.info(f"🎯 Hold Request Avg: {hold_time:.2f}ms")
        
        if hold_time < 100:
            logger.info("✅ PERFORMANCE TARGET MET: <100ms hold time")
        else:
            logger.info("❌ PERFORMANCE TARGET MISSED: >100ms hold time")
        
        if pool_time < 1:
            logger.info("✅ CONNECTION POOL OPTIMAL: <1ms get/return")
        else:
            logger.info("⚠️ CONNECTION POOL SLOW: >1ms get/return")
            
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")

if __name__ == "__main__":
    main()
